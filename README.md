# Theoriq Manager - 简单的任务管理框架

基于axios的简单Theoriq任务管理器，主要用于API调用和钱包管理。

## 特点

- 🔑 简单的钱包登录管理
- 📊 基本的统计和日志记录
- 🌐 代理支持
- 🔄 自动重试机制
- 💾 Token缓存
- 📁 任务文件分离

## 目录结构

```
theoriq/
├── TheoriqManager.js     # 主管理器
├── theoriq.js           # 命令行工具
├── config.json          # 配置文件
├── wallets.csv          # 钱包信息
├── package.json         # 项目配置
├── utils/
│   ├── WalletManager.js # 钱包管理
│   └── logger.js        # 日志工具
├── tasks/               # 任务目录
│   └── example-task.js  # 示例任务
└── logs/               # 日志文件
    └── tokens.json     # Token缓存
```

## 安装

```bash
npm install
```

## 配置

编辑 `config.json`：

```json
{
  "proxy": {
    "enabled": false,
    "url": "http://127.0.0.1:7890"
  },
  "theoriq": {
    "baseUrl": "https://api-theoriq.bonusblock.io",
    "blockchainName": "okx",
    "referralId": "optionalReferral"
  },
  "maxRetries": 3,
  "retryDelay": 2000,
  "requestTimeout": 30000
}
```

编辑 `wallets.csv`：

```csv
index,addr,pk
1,0x1234...,0xabcd...
2,0x5678...,0xefgh...
```

## 使用方法

### 基本命令

```bash
# 登录所有钱包
node theoriq.js login

# 登录指定钱包
node theoriq.js login 1 2 3
node theoriq.js login -i 1-5
node theoriq.js login -i 1,3,5

# 查看状态
node theoriq.js stats
node theoriq.js stats 1

# 查看配置
node theoriq.js config

# 帮助
node theoriq.js help
```

### 创建任务

在 `tasks/` 目录下创建任务文件，参考 `example-task.js`：

```javascript
const theoriqManager = require('../TheoriqManager')
const logger = require('../utils/logger')

async function myTask(walletIndex) {
  try {
    // 1. 检查登录状态
    const token = theoriqManager.getWalletToken(walletIndex)
    if (!token) {
      throw new Error('钱包未登录')
    }
    
    // 2. 发送API请求
    const result = await theoriqManager.sendAuthRequest(
      walletIndex,
      'POST',
      'https://api-theoriq.bonusblock.io/api/some-endpoint',
      { param1: 'value1' }
    )
    
    logger.success(`任务完成 - 钱包 ${walletIndex}`, result)
    return { success: true, data: result }
    
  } catch (error) {
    logger.error(`任务失败 - 钱包 ${walletIndex}`, error.message)
    return { success: false, error: error.message }
  }
}

module.exports = { myTask }
```

### 在代码中使用Manager

```javascript
const theoriqManager = require('./TheoriqManager')

// 登录钱包
const result = await theoriqManager.loginWallet(1)

// 批量登录
const results = await theoriqManager.loginAllWallets([1, 2, 3])

// 发送认证请求
const data = await theoriqManager.sendAuthRequest(
  1, 
  'GET', 
  'https://api-theoriq.bonusblock.io/api/endpoint'
)

// 获取统计信息
const stats = theoriqManager.getStats()
```

## 主要方法

### TheoriqManager

- `loginWallet(walletIndex)` - 登录单个钱包
- `loginAllWallets(walletIndexes)` - 批量登录钱包
- `sendAuthRequest(walletIndex, method, url, data)` - 发送认证请求
- `getWalletToken(walletIndex)` - 获取钱包token
- `getStats()` - 获取统计信息
- `showStats(walletIndex)` - 显示统计信息

### WalletManager

- `getWallet(index)` - 获取钱包信息
- `getAllWallets()` - 获取所有钱包
- `getWalletCount()` - 获取钱包数量

## 特性说明

### 自动重试
请求失败时自动重试，可在config.json中配置重试次数和延迟。

### Token缓存
登录成功的token会自动缓存在logs/tokens.json中，避免重复登录。

### 代理支持
支持HTTP/HTTPS代理，在config.json中配置。

### 日志记录
所有操作都会记录日志，包括成功、失败、警告等信息。

## 注意事项

1. 请确保钱包私钥的安全性
2. 根据实际的Theoriq API调整请求参数
3. 合理设置请求延迟，避免频率限制
4. 定期检查token有效性

## 开发说明

这是一个简单的框架，主要提供：
- 基础的钱包管理和登录功能
- HTTP请求封装和重试机制
- 简单的日志和统计
- 任务分离的架构

具体的Theoriq协议任务需要根据实际API文档进行开发。 