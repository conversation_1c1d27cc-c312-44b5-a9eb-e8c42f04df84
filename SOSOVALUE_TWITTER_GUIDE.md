# Sosovalue Twitter绑定项目使用指南

## 项目概述

这是一个完整的Sosovalue Twitter账号绑定自动化项目，已经从原有的代理请求系统迁移到使用原生axios进行HTTP请求。

## 修改内容

### 1. 代码修改
- ✅ 移除了 `requestWithProxy` 依赖
- ✅ 添加了 `makeRequest` 方法使用原生axios
- ✅ 移除了所有代理相关配置
- ✅ 保持了与原方法相同的接口和返回格式

### 2. 核心功能
- ✅ CF Token获取（Cloudflare防护）
- ✅ 用户登录（钱包签名认证）
- ✅ Twitter授权URL获取
- ✅ Twitter账号绑定

## 文件结构

```
sosovalue/
├── sosovalue.js              # 主要的Sosovalue类
test-sosovalue-twitter.js     # 完整测试脚本
SOSOVALUE_TWITTER_GUIDE.md    # 使用指南（本文件）
```

## 使用方法

### 准备工作

1. **配置钱包信息**
   - 确保 `wallets.csv` 文件包含钱包私钥
   - 确保 `twitter-bindings-example.csv` 文件包含Twitter auth token

2. **安装依赖**
   ```bash
   npm install fake-useragent
   ```

3. **启动验证码服务**
   - 确保本地验证码API服务运行在 `http://192.168.0.138:3000`
   - 该服务用于获取Cloudflare Turnstile token

### 运行测试

```bash
node test-sosovalue-twitter.js
```

## 测试流程详解

### 步骤1: 初始化钱包
- 从CSV文件读取钱包信息
- 创建ethers钱包实例
- 初始化Sosovalue类

### 步骤2: 获取CF Token
- 调用本地验证码API
- 获取Cloudflare Turnstile token
- 用于绕过网站防护

### 步骤3: 用户登录
- 获取登录nonce
- 使用钱包私钥签名消息
- 完成用户认证并获取token

### 步骤4: 获取Twitter授权URL
- 调用Sosovalue API获取Twitter OAuth URL
- 提取oauth_token参数

### 步骤5: Twitter授权处理
- 显示需要手动完成的授权步骤
- 提供详细的操作指南
- 模拟oauth_verifier获取（测试用）

### 步骤6: 完成Twitter绑定
- 使用oauth_token和oauth_verifier
- 调用绑定API完成账号关联

## 实际使用指南

### 获取真实的oauth_verifier

1. **打开授权URL**
   - 复制测试输出中的Twitter授权URL
   - 在浏览器中打开该URL

2. **完成Twitter授权**
   - 使用您的Twitter账号登录
   - 点击"授权应用"按钮

3. **获取验证码**
   - 授权后会重定向到回调URL
   - URL格式: `https://sosovalue.com?oauth_token=XXX&oauth_verifier=YYY`
   - 复制 `oauth_verifier` 参数的值

4. **完成绑定**
   ```javascript
   const result = await sosovalue.bindTwitter(token, oauthToken, realOauthVerifier);
   ```

## API方法说明

### Sosovalue类方法

#### `getLocalCaptcha()`
获取Cloudflare Turnstile token
- **返回**: Promise<string> - CF token

#### `login(inviteCode, captchaToken)`
用户登录
- **参数**: 
  - `inviteCode`: 邀请码（可选）
  - `captchaToken`: CF token
- **返回**: Promise<object> - 登录结果包含token

#### `getTwitterAuthUrl(token)`
获取Twitter授权URL
- **参数**: `token` - 用户登录token
- **返回**: Promise<object> - 包含授权URL和oauth_token

#### `bindTwitter(token, oauthToken, oauthVerifier)`
绑定Twitter账号
- **参数**:
  - `token`: 用户登录token
  - `oauthToken`: Twitter OAuth token
  - `oauthVerifier`: Twitter OAuth verifier
- **返回**: Promise<object> - 绑定结果

## 测试结果示例

```
🚀 开始Sosovalue Twitter绑定完整测试

=== 步骤1: 初始化钱包 ===
✅ 钱包初始化成功
   地址: 0xF998D473530DAd7450a4281f3a501a6CFe639f90

=== 步骤2: 获取CF Token ===
✅ CF Token获取成功
   Token: 0.4cBJDLbAPnvozjy4cj...

=== 步骤3: 用户登录 ===
✅ 用户登录成功
   Token: eyJ0eXAiOiJKc29uV2Vi...
   User ID: 1941447388594831362

=== 步骤4: 获取Twitter授权URL ===
✅ Twitter授权URL获取成功
   授权URL: https://api.twitter.com/oauth/authenticate?oauth_token=L6-95QAAAAABpaBwAAABmB5AIVQ

=== 步骤5: 处理Twitter授权 ===
✅ 模拟获取OAuth Verifier: mock_oauth_verifier_1752854174109

=== 步骤6: 完成Twitter绑定 ===
⚠️  绑定失败（预期结果，因为使用了模拟数据）

📋 测试总结:
   ✅ 钱包初始化
   ✅ CF Token获取
   ✅ 用户登录
   ✅ Twitter授权URL获取
   ✅ Twitter授权处理
   ⚠️  Twitter绑定（模拟）
```

## 错误处理

### 常见问题

1. **CF Token获取失败**
   - 检查验证码API服务是否运行
   - 确认API地址和端口正确

2. **用户登录失败**
   - 检查钱包私钥是否正确
   - 确认网络连接正常

3. **Twitter授权URL获取失败**
   - 检查用户token是否有效
   - 确认API端点可访问

4. **Twitter绑定失败**
   - 确认oauth_verifier是真实有效的
   - 检查oauth_token是否匹配

## 注意事项

1. **安全性**
   - 妥善保管钱包私钥
   - 不要在公共环境中运行

2. **网络要求**
   - 确保能访问Sosovalue API
   - 确保能访问Twitter API

3. **时效性**
   - OAuth token有时效性，需及时使用
   - 用户登录token也有过期时间

## 扩展功能

可以基于此框架扩展以下功能：
- 批量账号绑定
- 自动化Twitter操作
- 其他社交媒体绑定
- 定时任务执行

## 技术支持

如遇到问题，请检查：
1. 网络连接状态
2. API服务可用性
3. 配置文件正确性
4. 依赖包完整性
