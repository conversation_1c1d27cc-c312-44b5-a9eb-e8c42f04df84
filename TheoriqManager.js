const fs = require('fs')
const path = require('path')
const axios = require('axios')
const { ethers } = require('ethers')
const { v4: uuidv4 } = require('uuid')
const { HttpsProxyAgent } = require('https-proxy-agent')
const walletManager = require('./utils/WalletManager')
const logger = require('./utils/logger')

const BASE_URL = 'https://api-theoriq.bonusblock.io'

class TheoriqManager {
  constructor() {
    this.config = this.loadConfig()
    this.tokens = new Map() // 存储钱包token
    this.loadTokens()
  }

  /**
   * 加载配置
   */
  loadConfig() {
    try {
      const configPath = path.join(__dirname, 'config.json')
      if (fs.existsSync(configPath)) {
        return JSON.parse(fs.readFileSync(configPath, 'utf8'))
      }
    } catch (error) {
      logger.error('加载配置失败', error.message)
      throw error
    }
  }

  /**
   * 获取基础请求头
   */
  getBaseHeaders() {
    return {
      'accept': 'application/json, text/plain, */*',
      'accept-encoding': 'gzip, deflate, br, zstd',
      'accept-language': 'zh-CN,zh;q=0.9',
      'content-type': 'application/json',
      'origin': 'https://quests.theoriq.ai',
      'referer': 'https://quests.theoriq.ai/',
      'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"Windows"',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'cross-site',
      'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    }
  }

  /**
   * 获取认证请求头
   */
  getAuthHeaders(token) {
    return {
      ...this.getBaseHeaders(),
      'x-auth-token': token
    }
  }

  /**
   * 创建axios请求配置
   */
  getRequestConfig(token = null) {
    const config = {
      timeout: this.config.requestTimeout,
      headers: token ? this.getAuthHeaders(token) : this.getBaseHeaders()
    }

    // 添加代理配置
    if (this.config.proxy.enabled) {
      config.httpsAgent = new HttpsProxyAgent(this.config.proxy.url)
    }

    return config
  }

  /**
   * 带重试的HTTP请求
   */
  async makeRequest(method, url, data = null, token = null) {
    const config = this.getRequestConfig(token)
    
    for (let attempt = 0; attempt < this.config.maxRetries; attempt++) {
      try {
        let response
        if (method.toLowerCase() === 'get') {
          response = await axios.get(url, config)
        } else if (method.toLowerCase() === 'post') {
          response = await axios.post(url, data, config)
        } else {
          throw new Error(`不支持的HTTP方法: ${method}`)
        }
        
        return response.data
      } catch (error) {
        if (attempt === this.config.maxRetries - 1) {
          throw error
        }
        
        logger.warn(`请求失败，第${attempt + 1}次重试: ${error.message}`)
        await new Promise(resolve => setTimeout(resolve, this.config.retryDelay * (attempt + 1)))
      }
    }
  }

  /**
   * 获取认证票据
   */
  async getAuthTicket() {
    const nonce = uuidv4()
    const url = `${BASE_URL}/api/auth/get-auth-ticket`
    
    logger.info('获取认证票据', { nonce })
    
    const response = await this.makeRequest('POST', url, { nonce })
    
    if (response.success && response.payload) {
      logger.success('获取认证票据成功', { payload: response.payload })
      return { nonce, payload: response.payload }
    }
    
    throw new Error(`获取认证票据失败: ${JSON.stringify(response.errors)}`)
  }

  /**
   * 以太坊认证登录
   */
  async ethAuth(nonce, signedMessage) {
    const url = `${BASE_URL}/api/auth/eth`
    const data = {
      blockchainName: 'okx',
      signedMessage: signedMessage,
      nonce: nonce,
      referralId: 'optionalReferral'
    }
    
    logger.info('执行以太坊认证', { nonce, blockchainName: 'okx' })
    
    const response = await this.makeRequest('POST', url, data)
    
    if (response.success && response.payload) {
      const { account, session } = response.payload
      
      logger.success('以太坊认证成功', {
        userId: account.userId,
        expiresOn: session.expiresOn
      })
      
      return {
        account,
        token: session.token,
        expiresOn: session.expiresOn
      }
    }
    
    throw new Error(`以太坊认证失败: ${JSON.stringify(response.errors)}`)
  }

  /**
   * 钱包登录
   */
  async loginWallet(walletIndex) {
    try {
      const wallet = walletManager.getWallet(walletIndex)
      
      // 检查是否已有有效token
      const cachedToken = this.getWalletToken(walletIndex)
      if (cachedToken) {
        logger.info(`钱包 ${walletIndex} 使用缓存token`)
        return { success: true, fromCache: true, token: cachedToken }
      }
      
      logger.info(`开始登录钱包 ${walletIndex}`, { address: wallet.addr })
      
      // 1. 获取认证票据
      const { nonce, payload } = await this.getAuthTicket()
      
      // 2. 签名消息
      const ethWallet = new ethers.Wallet(wallet.pk)
      const signedMessage = await ethWallet.signMessage(payload)
      
      // 3. 以太坊认证
      const result = await this.ethAuth(nonce, signedMessage)
      
      // 4. 缓存token
      this.setWalletToken(walletIndex, {
        token: result.token,
        expiresOn: result.expiresOn,
        account: result.account,
        walletAddress: wallet.addr,
        loginTime: new Date().toISOString()
      })
      
      logger.success(`钱包 ${walletIndex} 登录成功`, {
        userId: result.account.userId,
        walletAddress: wallet.addr
      })
      
      return {
        success: true,
        fromCache: false,
        token: result.token,
        userId: result.account.userId,
        account: result.account
      }
      
    } catch (error) {
      logger.error(`钱包 ${walletIndex} 登录失败`, error.message)
      return { success: false, error: error.message }
    }
  }

  /**
   * 批量登录钱包
   */
  async loginAllWallets(walletIndexes = []) {
    const allWallets = walletManager.getAllWallets()
    const targetWallets = walletIndexes.length > 0 
      ? allWallets.filter(w => walletIndexes.includes(w.index))
      : allWallets
      
    logger.taskStart(`批量登录 ${targetWallets.length} 个钱包`)
    
    const results = []
    let success = 0, failed = 0, fromCache = 0
    
    for (const wallet of targetWallets) {
      const result = await this.loginWallet(wallet.index)
      results.push({ walletIndex: wallet.index, ...result })
      
      if (result.success) {
        success++
        if (result.fromCache) fromCache++
      } else {
        failed++
      }
      
      // 随机延迟
      if (wallet.index !== targetWallets[targetWallets.length - 1].index) {
        const delay = 1000 + Math.random() * 2000
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
    
    logger.taskEnd('批量登录', { total: targetWallets.length, success, failed, fromCache })
    
    return { total: targetWallets.length, success, failed, fromCache, results }
  }

  /**
   * 加载token缓存
   */
  loadTokens() {
    try {
      const tokenFile = path.join(__dirname, 'logs', 'tokens.json')
      if (fs.existsSync(tokenFile)) {
        const data = JSON.parse(fs.readFileSync(tokenFile, 'utf8'))
        
        // 清理过期token
        Object.entries(data).forEach(([walletIndex, tokenData]) => {
          if (new Date(tokenData.expiresOn) > new Date()) {
            this.tokens.set(parseInt(walletIndex), tokenData)
          }
        })
        
        logger.info(`加载了 ${this.tokens.size} 个有效token`)
      }
    } catch (error) {
      logger.warn('加载token失败', error.message)
    }
  }

  /**
   * 保存token缓存
   */
  saveTokens() {
    try {
      const logsDir = path.join(__dirname, 'logs')
      if (!fs.existsSync(logsDir)) {
        fs.mkdirSync(logsDir, { recursive: true })
      }
      
      const tokenFile = path.join(logsDir, 'tokens.json')
      const data = Object.fromEntries(this.tokens)
      fs.writeFileSync(tokenFile, JSON.stringify(data, null, 2))
    } catch (error) {
      logger.error('保存token失败', error.message)
    }
  }

  /**
   * 设置钱包token
   */
  setWalletToken(walletIndex, tokenData) {
    this.tokens.set(walletIndex, tokenData)
    this.saveTokens()
  }

  /**
   * 获取钱包token
   */
  getWalletToken(walletIndex) {
    const tokenData = this.tokens.get(walletIndex)
    if (tokenData && new Date(tokenData.expiresOn) > new Date()) {
      return tokenData.token
    }
    return null
  }

  /**
   * 获取钱包信息
   */
  getWalletInfo(walletIndex) {
    return this.tokens.get(walletIndex)
  }

  /**
   * 发送认证请求
   */
  async sendAuthRequest(walletIndex, method, url, data = null) {
    const token = this.getWalletToken(walletIndex)
    if (!token) {
      throw new Error(`钱包 ${walletIndex} 未登录或token已过期`)
    }
    
    return await this.makeRequest(method, url, data, token)
  }

  /**
   * 获取统计信息
   */
  getStats() {
    const allWallets = walletManager.getAllWallets()
    let loginCount = 0
    
    const stats = allWallets.map(wallet => {
      const tokenData = this.tokens.get(wallet.index)
      const isLoggedIn = tokenData && new Date(tokenData.expiresOn) > new Date()
      
      if (isLoggedIn) loginCount++
      
      return {
        walletIndex: wallet.index,
        address: wallet.addr,
        isLoggedIn,
        userId: tokenData?.account?.userId || null,
        expiresOn: tokenData?.expiresOn || null
      }
    })
    
    return {
      total: allWallets.length,
      loginCount,
      wallets: stats
    }
  }

  /**
   * 显示统计信息
   */
  showStats(walletIndex = null) {
    if (walletIndex) {
      // 显示单个钱包信息
      const wallet = walletManager.getWallet(walletIndex)
      const tokenData = this.tokens.get(walletIndex)
      
      console.log(`\n=== 钱包 ${walletIndex} 信息 ===`)
      console.log(`地址: ${wallet.addr}`)
      
      if (tokenData) {
        const isValid = new Date(tokenData.expiresOn) > new Date()
        console.log(`状态: ${isValid ? '已登录' : 'Token已过期'}`)
        console.log(`用户ID: ${tokenData.account.userId}`)
        console.log(`过期时间: ${tokenData.expiresOn}`)
      } else {
        console.log(`状态: 未登录`)
      }
    } else {
      // 显示所有钱包统计
      const stats = this.getStats()
      
      console.log(`\n=== Theoriq Manager 统计 ===`)
      console.log(`总钱包数: ${stats.total}`)
      console.log(`已登录: ${stats.loginCount}`)
      console.log(`未登录: ${stats.total - stats.loginCount}`)
      
      console.log(`\n钱包详情:`)
      stats.wallets.forEach(w => {
        const status = w.isLoggedIn ? '✅已登录' : '❌未登录'
        console.log(`  钱包${w.walletIndex}: ${status} ${w.userId || ''}`)
      })
    }
  }
}

// 创建单例
const theoriqManager = new TheoriqManager()

module.exports = theoriqManager