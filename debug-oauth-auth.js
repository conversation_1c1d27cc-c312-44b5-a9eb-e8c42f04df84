const Twitter = require('./x/twitter')
const axios = require('axios')
const logger = require('./utils/logger')

/**
 * 调试OAuth授权过程
 */
async function debugOAuthAuthorization() {
  try {
    console.log('=== 调试OAuth授权过程 ===\n')
    
    // 从CSV读取最新的OAuth token
    const fs = require('fs')
    const csvContent = fs.readFileSync('./twitter-bindings-example.csv', 'utf8')
    const lines = csvContent.trim().split('\n')
    const [walletIndex, twitterAuthToken, oauthToken] = lines[1].split(',')
    
    console.log(`Twitter Auth Token: ${twitterAuthToken.substring(0, 10)}...`)
    console.log(`OAuth Token: ${oauthToken}`)
    
    // 创建Twitter实例
    const twitter = new Twitter(twitterAuthToken.trim())
    
    console.log('\n1. 测试获取authenticity_token...')
    const tokenResult = await twitter.getTwitterToken(oauthToken.trim())
    
    if (tokenResult) {
      console.log('✅ 获取authenticity_token成功')
      console.log(`   Token: ${twitter.getAuthenticityToken()?.substring(0, 20)}...`)
      
      console.log('\n2. 调试OAuth授权请求...')
      
      // 准备POST数据
      const postData = new URLSearchParams({
        'authenticity_token': twitter.getAuthenticityToken(),
        'redirect_after_login': `https://api.x.com/oauth/authorize?oauth_token=${oauthToken.trim()}`,
        'oauth_token': oauthToken.trim()
      })
      
      console.log('POST数据:')
      console.log(`  authenticity_token: ${twitter.getAuthenticityToken()?.substring(0, 20)}...`)
      console.log(`  redirect_after_login: https://api.x.com/oauth/authorize?oauth_token=${oauthToken.trim()}`)
      console.log(`  oauth_token: ${oauthToken.trim()}`)
      
      // 准备请求配置
      const config = {
        timeout: 120000,
        headers: {
          ...twitter.defaultHeaders,
          "cookie": twitter.defaultCookies,
          'content-type': 'application/x-www-form-urlencoded'
        },
        maxRedirects: 5,
        validateStatus: status => status >= 200 && status < 500
      }
      
      console.log('\n3. 发送OAuth授权请求...')
      
      try {
        const response = await axios.post(
          'https://api.x.com/oauth/authorize',
          postData,
          config
        )
        
        console.log(`响应状态码: ${response.status}`)
        console.log(`响应头: ${JSON.stringify(response.headers, null, 2)}`)
        
        const responseText = typeof response.data === 'string' ? response.data : JSON.stringify(response.data)
        console.log(`响应长度: ${responseText.length} 字符`)
        
        // 查找oauth_verifier
        if (responseText.includes('oauth_verifier')) {
          console.log('✅ 响应中包含oauth_verifier')
          
          const verifierMatch = responseText.match(/oauth_verifier=([^"&]+)/);
          if (verifierMatch && verifierMatch[1]) {
            console.log(`✅ 找到OAuth Verifier: ${verifierMatch[1]}`)
            return verifierMatch[1]
          } else {
            console.log('❌ 无法解析oauth_verifier')
          }
        } else {
          console.log('❌ 响应中不包含oauth_verifier')
          
          // 检查是否需要用户授权
          if (responseText.includes('authorize') || responseText.includes('Authorize')) {
            console.log('⚠️  可能需要用户手动授权')
            console.log('响应可能包含授权页面，需要用户点击"授权"按钮')
          }
          
          // 检查是否有错误信息
          if (responseText.includes('error') || responseText.includes('Error')) {
            console.log('⚠️  响应可能包含错误信息')
          }
          
          // 保存响应到文件以便检查
          fs.writeFileSync('./debug_oauth_response.html', responseText)
          console.log('📄 响应已保存到 debug_oauth_response.html')
        }
        
      } catch (error) {
        console.log(`❌ OAuth授权请求失败: ${error.message}`)
        if (error.response) {
          console.log(`   状态码: ${error.response.status}`)
          console.log(`   响应: ${error.response.data?.substring(0, 500)}...`)
        }
      }
      
    } else {
      console.log('❌ 获取authenticity_token失败')
    }
    
  } catch (error) {
    console.error('调试过程中发生错误:', error.message)
  }
}

/**
 * 尝试不同的OAuth授权方法
 */
async function tryDifferentAuthMethods() {
  try {
    console.log('=== 尝试不同的OAuth授权方法 ===\n')
    
    const fs = require('fs')
    const csvContent = fs.readFileSync('./twitter-bindings-example.csv', 'utf8')
    const lines = csvContent.trim().split('\n')
    const [walletIndex, twitterAuthToken, oauthToken] = lines[1].split(',')
    
    const twitter = new Twitter(twitterAuthToken.trim())
    
    // 先获取authenticity_token
    await twitter.getTwitterToken(oauthToken.trim())
    
    const methods = [
      {
        name: '方法1: 标准POST到/oauth/authorize',
        url: 'https://api.x.com/oauth/authorize',
        data: new URLSearchParams({
          'authenticity_token': twitter.getAuthenticityToken(),
          'redirect_after_login': `https://api.x.com/oauth/authorize?oauth_token=${oauthToken.trim()}`,
          'oauth_token': oauthToken.trim()
        }),
        headers: { 'content-type': 'application/x-www-form-urlencoded' }
      },
      {
        name: '方法2: 添加authorize=1参数',
        url: 'https://api.x.com/oauth/authorize',
        data: new URLSearchParams({
          'authenticity_token': twitter.getAuthenticityToken(),
          'redirect_after_login': `https://api.x.com/oauth/authorize?oauth_token=${oauthToken.trim()}`,
          'oauth_token': oauthToken.trim(),
          'authorize': '1'
        }),
        headers: { 'content-type': 'application/x-www-form-urlencoded' }
      },
      {
        name: '方法3: 使用twitter.com域名',
        url: 'https://twitter.com/oauth/authorize',
        data: new URLSearchParams({
          'authenticity_token': twitter.getAuthenticityToken(),
          'redirect_after_login': `https://twitter.com/oauth/authorize?oauth_token=${oauthToken.trim()}`,
          'oauth_token': oauthToken.trim()
        }),
        headers: { 'content-type': 'application/x-www-form-urlencoded' }
      }
    ]
    
    for (const method of methods) {
      console.log(`\n--- ${method.name} ---`)
      
      try {
        const config = {
          timeout: 30000,
          headers: {
            ...twitter.defaultHeaders,
            "cookie": twitter.defaultCookies,
            ...method.headers
          },
          maxRedirects: 5,
          validateStatus: status => status >= 200 && status < 500
        }
        
        const response = await axios.post(method.url, method.data, config)
        
        console.log(`状态码: ${response.status}`)
        
        const responseText = typeof response.data === 'string' ? response.data : JSON.stringify(response.data)
        
        if (responseText.includes('oauth_verifier')) {
          const verifierMatch = responseText.match(/oauth_verifier=([^"&]+)/);
          if (verifierMatch && verifierMatch[1]) {
            console.log(`✅ 成功! OAuth Verifier: ${verifierMatch[1]}`)
            return verifierMatch[1]
          }
        } else {
          console.log('❌ 未找到oauth_verifier')
        }
        
      } catch (error) {
        console.log(`❌ 失败: ${error.message}`)
      }
    }
    
    console.log('\n所有方法都失败了')
    
  } catch (error) {
    console.error('尝试不同方法时发生错误:', error.message)
  }
}

/**
 * 检查授权URL的响应
 */
async function checkAuthUrl() {
  try {
    console.log('=== 检查授权URL响应 ===\n')
    
    const fs = require('fs')
    const csvContent = fs.readFileSync('./twitter-bindings-example.csv', 'utf8')
    const lines = csvContent.trim().split('\n')
    const [walletIndex, twitterAuthToken, oauthToken] = lines[1].split(',')
    
    const authUrl = `https://api.x.com/oauth/authorize?oauth_token=${oauthToken.trim()}`
    console.log(`授权URL: ${authUrl}`)
    
    const twitter = new Twitter(twitterAuthToken.trim())
    
    try {
      const response = await axios.get(authUrl, {
        headers: {
          ...twitter.defaultHeaders,
          "cookie": twitter.defaultCookies
        },
        timeout: 30000,
        maxRedirects: 5
      })
      
      console.log(`状态码: ${response.status}`)
      
      const responseText = response.data
      console.log(`响应长度: ${responseText.length} 字符`)
      
      // 检查页面内容
      if (responseText.includes('Authorize')) {
        console.log('✅ 页面包含"Authorize"，这是正常的授权页面')
      }
      
      if (responseText.includes('authenticity_token')) {
        const tokenMatch = responseText.match(/authenticity_token" value="([^"]+)"/);
        if (tokenMatch && tokenMatch[1]) {
          console.log(`✅ 找到authenticity_token: ${tokenMatch[1].substring(0, 20)}...`)
        }
      }
      
      // 保存页面以便检查
      fs.writeFileSync('./auth_page.html', responseText)
      console.log('📄 授权页面已保存到 auth_page.html')
      
    } catch (error) {
      console.log(`❌ 访问授权URL失败: ${error.message}`)
    }
    
  } catch (error) {
    console.error('检查授权URL时发生错误:', error.message)
  }
}

// 主程序
if (require.main === module) {
  const args = process.argv.slice(2)
  
  if (args[0] === 'methods') {
    tryDifferentAuthMethods()
  } else if (args[0] === 'url') {
    checkAuthUrl()
  } else {
    debugOAuthAuthorization()
  }
}

module.exports = { debugOAuthAuthorization, tryDifferentAuthMethods, checkAuthUrl }
