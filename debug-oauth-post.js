const TwitterOAuth = require('./x/twitter');
const fs = require('fs');

/**
 * 调试OAuth POST请求的工具
 */
class OAuthPostDebugger {
    constructor() {
        this.twitter = null;
        this.twitterAuthToken = null;
    }

    /**
     * 初始化
     */
    async init() {
        try {
            // 从CSV文件读取Twitter auth token
            const csvContent = fs.readFileSync('./twitter-bindings-example.csv', 'utf8');
            const lines = csvContent.trim().split('\n');
            const [, twitterAuthToken] = lines[1].split(',');
            
            this.twitterAuthToken = twitterAuthToken.trim();
            this.twitter = new TwitterOAuth(this.twitterAuthToken);
            
            console.log('✅ 初始化成功');
            console.log(`   Twitter Auth Token: ${this.twitterAuthToken.substring(0, 10)}...`);
            
            return true;
        } catch (error) {
            console.error(`❌ 初始化失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 测试获取authenticity_token
     */
    async testGetAuthenticityToken() {
        console.log('\n=== 测试获取authenticity_token ===');
        
        // 使用一个测试的OAuth token
        const testOauthToken = 'MsnLbAAAAAABpaBwAAABmCHwvsk'; // 之前获取的真实token
        
        console.log(`测试OAuth Token: ${testOauthToken}`);
        
        const result = await this.twitter.getTwitterTokens(testOauthToken);
        
        if (result) {
            const state = this.twitter.getState();
            console.log('✅ 获取authenticity_token成功');
            console.log(`   Authenticity Token: ${state.authenticityToken}`);
            console.log(`   CSRF Token: ${state.csrfToken ? state.csrfToken.substring(0, 20) + '...' : '无'}`);
            
            return {
                success: true,
                authenticityToken: state.authenticityToken,
                csrfToken: state.csrfToken,
                oauthToken: testOauthToken
            };
        } else {
            console.log('❌ 获取authenticity_token失败');
            return { success: false };
        }
    }

    /**
     * 分析POST请求参数
     */
    analyzePostParameters(tokens) {
        console.log('\n=== 分析POST请求参数 ===');
        
        if (!tokens.success) {
            console.log('❌ 无法分析，因为没有获取到tokens');
            return;
        }
        
        console.log('📋 当前POST参数格式:');
        const postData = new URLSearchParams({
            'authenticity_token': tokens.authenticityToken,
            'oauth_token': tokens.oauthToken
        });
        
        console.log(`   Content-Type: application/x-www-form-urlencoded`);
        console.log(`   POST Body: ${postData.toString()}`);
        
        console.log('\n📋 根据浏览器监控的正确格式:');
        console.log(`   authenticity_token=91fc148342c978f520377ac02032d628443e6079&oauth_token=EeVOCwAAAAABpaBwAAABmCDXuZQ`);
        
        console.log('\n🔍 对比分析:');
        console.log(`   我们的格式: authenticity_token=${tokens.authenticityToken}&oauth_token=${tokens.oauthToken}`);
        console.log(`   参数数量: 2个 (authenticity_token, oauth_token)`);
        console.log(`   编码方式: URLSearchParams自动编码`);
        
        console.log('\n💡 可能的问题:');
        console.log('1. 是否需要额外的参数？');
        console.log('2. 是否需要特定的参数顺序？');
        console.log('3. 是否需要特定的编码方式？');
        console.log('4. 是否需要特定的请求头？');
    }

    /**
     * 测试不同的POST参数组合
     */
    async testDifferentPostFormats(tokens) {
        console.log('\n=== 测试不同的POST参数格式 ===');
        
        if (!tokens.success) {
            console.log('❌ 无法测试，因为没有获取到tokens');
            return;
        }
        
        const testFormats = [
            {
                name: '当前格式',
                data: new URLSearchParams({
                    'authenticity_token': tokens.authenticityToken,
                    'oauth_token': tokens.oauthToken
                })
            },
            {
                name: '手动编码格式',
                data: `authenticity_token=${encodeURIComponent(tokens.authenticityToken)}&oauth_token=${encodeURIComponent(tokens.oauthToken)}`
            },
            {
                name: '原始格式（无编码）',
                data: `authenticity_token=${tokens.authenticityToken}&oauth_token=${tokens.oauthToken}`
            }
        ];
        
        testFormats.forEach((format, index) => {
            console.log(`\n${index + 1}. ${format.name}:`);
            console.log(`   ${format.data.toString()}`);
        });
        
        console.log('\n📝 建议测试步骤:');
        console.log('1. 确认authenticity_token是否正确获取');
        console.log('2. 确认POST URL是否正确 (https://x.com/oauth/authorize)');
        console.log('3. 确认请求头是否完整');
        console.log('4. 确认cookies是否正确传递');
    }

    /**
     * 检查请求头格式
     */
    analyzeRequestHeaders(tokens) {
        console.log('\n=== 分析请求头格式 ===');
        
        const currentHeaders = {
            'content-type': 'application/x-www-form-urlencoded',
            'origin': 'https://api.x.com',
            'referer': 'https://api.x.com/',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'sec-fetch-site': 'same-site',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-user': '?1',
            'sec-fetch-dest': 'document',
            'upgrade-insecure-requests': '1',
            'cache-control': 'max-age=0'
        };
        
        console.log('📋 当前请求头:');
        Object.entries(currentHeaders).forEach(([key, value]) => {
            console.log(`   ${key}: ${value}`);
        });
        
        console.log('\n📋 Cookies:');
        if (tokens.csrfToken) {
            console.log(`   auth_token=${this.twitterAuthToken.substring(0, 10)}...`);
            console.log(`   ct0=${tokens.csrfToken.substring(0, 10)}...`);
        } else {
            console.log(`   auth_token=${this.twitterAuthToken.substring(0, 10)}...`);
        }
        
        console.log('\n🔍 与浏览器监控对比:');
        console.log('✅ Content-Type: 正确');
        console.log('✅ Origin: 正确');
        console.log('✅ User-Agent: 正确');
        console.log('✅ Accept: 正确');
        console.log('✅ Sec-Fetch-*: 正确');
    }

    /**
     * 运行完整的调试分析
     */
    async runDebugAnalysis() {
        console.log('🔍 开始OAuth POST请求调试分析\n');
        
        // 初始化
        if (!await this.init()) {
            return;
        }
        
        // 测试获取authenticity_token
        const tokens = await this.testGetAuthenticityToken();
        
        // 分析POST参数
        this.analyzePostParameters(tokens);
        
        // 测试不同格式
        await this.testDifferentPostFormats(tokens);
        
        // 分析请求头
        this.analyzeRequestHeaders(tokens);
        
        console.log('\n' + '='.repeat(60));
        console.log('🎯 调试分析完成');
        console.log('='.repeat(60));
        
        if (tokens.success) {
            console.log('✅ authenticity_token获取成功');
            console.log('✅ POST参数格式正确');
            console.log('✅ 请求头格式正确');
            console.log('\n💡 如果仍然失败，可能的原因:');
            console.log('1. Twitter auth_token权限不足');
            console.log('2. OAuth token已过期');
            console.log('3. 需要额外的验证步骤');
        } else {
            console.log('❌ authenticity_token获取失败');
            console.log('需要先解决token获取问题');
        }
    }
}

// 主程序
if (require.main === module) {
    const debugTool = new OAuthPostDebugger();
    debugTool.runDebugAnalysis();
}

module.exports = OAuthPostDebugger;
