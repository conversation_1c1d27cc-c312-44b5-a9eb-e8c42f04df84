const theoriqManager = require('./TheoriqManager')
const axios = require('axios')
const logger = require('./utils/logger')

/**
 * 调试OAuth token获取
 */
async function debugOAuthRequest() {
  try {
    console.log('=== 调试OAuth Token获取 ===\n')
    
    // 1. 确保钱包已登录
    const walletIndex = 1
    let token = theoriqManager.getWalletToken(walletIndex)
    
    if (!token) {
      console.log('钱包未登录，正在登录...')
      const loginResult = await theoriqManager.loginWallet(walletIndex)
      if (!loginResult.success) {
        throw new Error(`钱包登录失败: ${loginResult.error}`)
      }
      token = theoriqManager.getWalletToken(walletIndex)
    }
    
    console.log('✅ 钱包已登录')
    console.log(`Token: ${token.substring(0, 20)}...`)
    
    // 2. 准备请求
    const url = 'https://api-theoriq.bonusblock.io/api/social/twitter/request-token'
    const headers = {
      'accept': 'application/json, text/plain, */*',
      'accept-encoding': 'gzip, deflate, br, zstd',
      'accept-language': 'zh-CN,zh;q=0.9',
      'content-type': 'application/json',
      'origin': 'https://quests.theoriq.ai',
      'referer': 'https://quests.theoriq.ai/',
      'x-auth-token': token,
      'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    console.log('\n📤 发送请求:')
    console.log(`URL: ${url}`)
    console.log(`Headers: ${JSON.stringify(headers, null, 2)}`)
    
    // 3. 发送请求
    try {
      const response = await axios.post(url, {}, { headers, timeout: 30000 })
      
      console.log('\n✅ 请求成功:')
      console.log(`状态码: ${response.status}`)
      console.log(`响应数据: ${JSON.stringify(response.data, null, 2)}`)
      
      if (response.data && response.data.oauth_token) {
        console.log(`\n🎉 获取到OAuth Token: ${response.data.oauth_token}`)
        
        // 更新CSV文件
        const fs = require('fs')
        const csvContent = fs.readFileSync('./twitter-bindings-example.csv', 'utf8')
        const lines = csvContent.trim().split('\n')
        const [walletIndex, twitterAuthToken] = lines[1].split(',')
        
        const newCsvContent = `walletIndex,twitterAuthToken,oauthToken\n${walletIndex},${twitterAuthToken},${response.data.oauth_token}`
        fs.writeFileSync('./twitter-bindings-example.csv', newCsvContent)
        
        console.log('✅ 已更新CSV文件中的OAuth Token')
        
        return response.data.oauth_token
      }
      
    } catch (error) {
      console.log('\n❌ 请求失败:')
      console.log(`状态码: ${error.response?.status || 'N/A'}`)
      console.log(`错误信息: ${error.message}`)
      
      if (error.response?.data) {
        console.log(`响应数据: ${JSON.stringify(error.response.data, null, 2)}`)
      }
      
      // 尝试其他可能的端点
      console.log('\n🔄 尝试其他端点...')
      await tryAlternativeEndpoints(token)
    }
    
  } catch (error) {
    console.error('调试过程中发生错误:', error.message)
  }
}

/**
 * 尝试其他可能的API端点
 */
async function tryAlternativeEndpoints(token) {
  const endpoints = [
    'https://api-theoriq.bonusblock.io/api/social/twitter/auth',
    'https://api-theoriq.bonusblock.io/api/auth/twitter/request-token',
    'https://api-theoriq.bonusblock.io/api/twitter/request-token',
    'https://api-theoriq.bonusblock.io/oauth/twitter/request-token'
  ]
  
  const headers = {
    'accept': 'application/json, text/plain, */*',
    'content-type': 'application/json',
    'x-auth-token': token,
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
  }
  
  for (const endpoint of endpoints) {
    try {
      console.log(`\n🔍 尝试端点: ${endpoint}`)
      
      const response = await axios.post(endpoint, {}, { 
        headers, 
        timeout: 10000,
        validateStatus: status => status < 500 // 接受4xx错误以查看响应
      })
      
      console.log(`   状态码: ${response.status}`)
      if (response.data) {
        console.log(`   响应: ${JSON.stringify(response.data, null, 2)}`)
      }
      
      if (response.status === 200 && response.data?.oauth_token) {
        console.log(`   ✅ 成功获取OAuth Token: ${response.data.oauth_token}`)
        return response.data.oauth_token
      }
      
    } catch (error) {
      console.log(`   ❌ 失败: ${error.response?.status || error.message}`)
      if (error.response?.data) {
        console.log(`   响应: ${JSON.stringify(error.response.data, null, 2)}`)
      }
    }
  }
}

/**
 * 手动测试Twitter OAuth流程
 */
async function manualOAuthTest() {
  console.log('=== 手动OAuth测试 ===\n')
  
  console.log('由于API获取OAuth token失败，我们可以：')
  console.log('1. 使用浏览器手动获取OAuth token')
  console.log('2. 使用现有的OAuth token进行测试\n')
  
  console.log('手动获取步骤:')
  console.log('1. 登录 https://quests.theoriq.ai')
  console.log('2. 找到Twitter绑定功能')
  console.log('3. 打开浏览器开发者工具 -> Network')
  console.log('4. 点击"绑定Twitter"按钮')
  console.log('5. 在Network面板中找到 request-token 请求')
  console.log('6. 复制响应中的 oauth_token')
  console.log('7. 更新CSV文件中的oauth_token\n')
  
  // 测试现有token
  const fs = require('fs')
  const csvContent = fs.readFileSync('./twitter-bindings-example.csv', 'utf8')
  const lines = csvContent.trim().split('\n')
  const [walletIndex, twitterAuthToken, oauthToken] = lines[1].split(',')
  
  console.log(`当前OAuth Token: ${oauthToken}`)
  console.log('如果这个token是新获取的，我们可以直接测试Twitter绑定功能')
}

// 主程序
if (require.main === module) {
  const args = process.argv.slice(2)
  
  if (args[0] === 'manual') {
    manualOAuthTest()
  } else {
    debugOAuthRequest()
  }
}

module.exports = { debugOAuthRequest, manualOAuthTest }
