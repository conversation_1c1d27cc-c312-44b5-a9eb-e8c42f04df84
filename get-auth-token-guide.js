const readline = require('readline');
const axios = require('axios');

/**
 * 获取有效Twitter Auth Token的指导工具
 */
class AuthTokenGuide {
    constructor() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
    }

    /**
     * 获取用户输入
     */
    async getUserInput(question) {
        return new Promise((resolve) => {
            this.rl.question(question, (answer) => {
                resolve(answer.trim());
            });
        });
    }

    /**
     * 显示获取auth_token的详细指导
     */
    showDetailedGuide() {
        console.log('=== 获取有效Twitter Auth Token详细指导 ===\n');
        
        console.log('📋 步骤说明:');
        console.log('1. 打开Chrome浏览器');
        console.log('2. 访问 https://x.com (确保已登录你的Twitter账号)');
        console.log('3. 按F12打开开发者工具');
        console.log('4. 点击顶部的 "Application" 标签');
        console.log('5. 在左侧面板中找到 "Storage" → "Cookies" → "https://x.com"');
        console.log('6. 在右侧的cookies列表中找到名为 "auth_token" 的条目');
        console.log('7. 双击 "auth_token" 的值进行复制');
        console.log('8. 将复制的值粘贴到下面的提示中\n');
        
        console.log('🔍 auth_token特征:');
        console.log('- 长度通常是40个字符');
        console.log('- 由数字和小写字母组成');
        console.log('- 例如: 53f0a58127e04d5c345bd63f5912ee36b5858c3d\n');
        
        console.log('⚠️  注意事项:');
        console.log('- 确保你已经登录Twitter账号');
        console.log('- auth_token是敏感信息，请妥善保管');
        console.log('- 如果token无效，请重新登录Twitter后再获取\n');
    }

    /**
     * 验证auth_token是否有效
     */
    async validateAuthToken(authToken) {
        try {
            console.log('🔍 正在验证auth_token有效性...');
            
            const response = await axios.get('https://api.twitter.com/oauth/authenticate?oauth_token=test_validation', {
                headers: {
                    'cookie': `auth_token=${authToken}`,
                    'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
                },
                validateStatus: status => status >= 200 && status < 500,
                timeout: 10000
            });
            
            if (response.status === 200) {
                console.log('✅ auth_token验证成功！');
                return true;
            } else if (response.status === 403) {
                console.log('❌ auth_token无效或已过期');
                return false;
            } else {
                console.log(`⚠️  验证返回状态码: ${response.status}`);
                return false;
            }
            
        } catch (error) {
            console.log(`❌ 验证过程中出错: ${error.message}`);
            return false;
        }
    }

    /**
     * 更新CSV文件
     */
    async updateCSVFile(authToken) {
        try {
            const fs = require('fs');
            const csvContent = `walletIndex,twitterAuthToken\n1,${authToken}`;
            fs.writeFileSync('./twitter-bindings-example.csv', csvContent);
            console.log('✅ CSV文件已更新');
            return true;
        } catch (error) {
            console.log(`❌ 更新CSV文件失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 运行完整的指导流程
     */
    async runGuide() {
        try {
            console.log('🚀 Twitter Auth Token获取指导工具\n');
            
            // 显示详细指导
            this.showDetailedGuide();
            
            // 获取用户输入的auth_token
            const authToken = await this.getUserInput('请输入从浏览器获取的auth_token: ');
            
            if (!authToken || authToken.length < 20) {
                console.log('❌ 输入的auth_token格式不正确');
                this.rl.close();
                return;
            }
            
            console.log(`\n收到auth_token: ${authToken.substring(0, 10)}...`);
            
            // 验证auth_token
            const isValid = await this.validateAuthToken(authToken);
            
            if (isValid) {
                // 更新CSV文件
                const updated = await this.updateCSVFile(authToken);
                
                if (updated) {
                    console.log('\n🎉 设置完成！');
                    console.log('现在可以运行Twitter绑定流程了:');
                    console.log('  node sosovalue-twitter-bind.js');
                    console.log('或者测试修复后的OAuth流程:');
                    console.log('  node test-fixed-oauth.js');
                } else {
                    console.log('\n❌ 更新配置文件失败');
                }
            } else {
                console.log('\n❌ auth_token验证失败');
                console.log('请检查:');
                console.log('1. 是否已登录Twitter账号');
                console.log('2. 是否复制了完整的auth_token');
                console.log('3. 是否从正确的位置复制');
                
                const retry = await this.getUserInput('\n是否要重新尝试? (y/n): ');
                if (retry.toLowerCase() === 'y') {
                    this.rl.close();
                    // 重新运行
                    const newGuide = new AuthTokenGuide();
                    await newGuide.runGuide();
                    return;
                }
            }
            
            this.rl.close();
            
        } catch (error) {
            console.log(`\n❌ 运行过程中出错: ${error.message}`);
            this.rl.close();
        }
    }

    /**
     * 显示当前配置状态
     */
    async showCurrentStatus() {
        try {
            console.log('=== 当前配置状态 ===\n');
            
            const fs = require('fs');
            const csvContent = fs.readFileSync('./twitter-bindings-example.csv', 'utf8');
            const lines = csvContent.trim().split('\n');
            const [, currentAuthToken] = lines[1].split(',');
            
            console.log(`当前auth_token: ${currentAuthToken.substring(0, 10)}...`);
            
            // 验证当前token
            const isValid = await this.validateAuthToken(currentAuthToken);
            
            if (isValid) {
                console.log('✅ 当前auth_token有效');
                console.log('可以直接运行Twitter绑定流程');
            } else {
                console.log('❌ 当前auth_token无效');
                console.log('需要获取新的auth_token');
            }
            
        } catch (error) {
            console.log(`❌ 检查状态失败: ${error.message}`);
        }
    }
}

// 主程序
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args[0] === 'status') {
        const guide = new AuthTokenGuide();
        guide.showCurrentStatus().then(() => {
            guide.rl.close();
        });
    } else {
        const guide = new AuthTokenGuide();
        guide.runGuide();
    }
}

module.exports = AuthTokenGuide;
