const theoriqManager = require('./TheoriqManager')
const logger = require('./utils/logger')

/**
 * 使用正确的API端点获取Twitter OAuth token
 * @param {number} walletIndex 钱包索引
 * @returns {Object} 包含oauth_token的结果
 */
async function getCorrectTwitterOAuthToken(walletIndex) {
  try {
    logger.info(`获取Twitter OAuth token - 钱包 ${walletIndex}`)
    
    // 1. 确保钱包已登录
    let token = theoriqManager.getWalletToken(walletIndex)
    if (!token) {
      console.log(`钱包 ${walletIndex} 未登录，正在登录...`)
      const loginResult = await theoriqManager.loginWallet(walletIndex)
      if (!loginResult.success) {
        throw new Error(`钱包登录失败: ${loginResult.error}`)
      }
      token = theoriqManager.getWalletToken(walletIndex)
    }
    
    console.log(`✅ 钱包 ${walletIndex} 已登录`)
    
    // 2. 使用正确的API端点请求Twitter OAuth token
    const result = await theoriqManager.sendAuthRequest(
      walletIndex,
      'POST',
      'https://api-theoriq.bonusblock.io/api/auth/social-link',
      {
        "social": "twitter",
        "returnTo": "https://quests.theoriq.ai/quests"
      }
    )
    
    console.log('API响应:', JSON.stringify(result, null, 2))
    
    if (result && result.success && result.payload) {
      // 解析授权URL中的oauth_token
      const authUrl = result.payload
      console.log(`授权URL: ${authUrl}`)
      
      // 从URL中提取oauth_token
      const urlParams = new URL(authUrl).searchParams
      const oauthToken = urlParams.get('oauth_token')
      
      if (oauthToken) {
        logger.success(`获取Twitter OAuth token成功 - 钱包 ${walletIndex}`, {
          oauth_token: oauthToken,
          auth_url: authUrl
        })
        
        return {
          success: true,
          oauth_token: oauthToken,
          auth_url: authUrl
        }
      } else {
        throw new Error(`无法从授权URL中提取oauth_token: ${authUrl}`)
      }
    } else {
      throw new Error(`API返回格式错误: ${JSON.stringify(result)}`)
    }
    
  } catch (error) {
    logger.error(`获取Twitter OAuth token失败 - 钱包 ${walletIndex}`, error.message)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 尝试不同的请求参数
 */
async function tryDifferentParams(walletIndex) {
  const token = theoriqManager.getWalletToken(walletIndex)
  if (!token) {
    throw new Error('钱包未登录')
  }
  
  const testParams = [
    {
      "social": "twitter",
      "returnTo": "https://quests.theoriq.ai/quests"
    },
    { platform: 'twitter' },
    { provider: 'twitter' },
    { social: 'twitter' },
    { type: 'twitter' },
    {}, // 空参数
    { service: 'twitter', action: 'link' }
  ]
  
  console.log('\n🔍 尝试不同的请求参数...')
  
  for (let i = 0; i < testParams.length; i++) {
    const params = testParams[i]
    console.log(`\n--- 测试参数 ${i + 1}: ${JSON.stringify(params)} ---`)
    
    try {
      const result = await theoriqManager.sendAuthRequest(
        walletIndex,
        'POST',
        'https://api-theoriq.bonusblock.io/api/auth/social-link',
        params
      )
      
      console.log(`响应: ${JSON.stringify(result, null, 2)}`)
      
      if (result && result.success && result.payload) {
        const authUrl = result.payload
        const urlParams = new URL(authUrl).searchParams
        const oauthToken = urlParams.get('oauth_token')
        
        if (oauthToken) {
          console.log(`✅ 成功获取OAuth token: ${oauthToken}`)
          return {
            success: true,
            oauth_token: oauthToken,
            auth_url: authUrl,
            params: params
          }
        }
      }
      
    } catch (error) {
      console.log(`❌ 失败: ${error.message}`)
    }
  }
  
  return { success: false, error: '所有参数都失败了' }
}

/**
 * 更新CSV文件并测试Twitter绑定
 */
async function updateAndTest(walletIndex) {
  try {
    console.log('=== 获取OAuth Token并测试绑定 ===\n')
    
    // 1. 获取OAuth token
    const result = await getCorrectTwitterOAuthToken(walletIndex)
    
    if (!result.success) {
      console.log('❌ 获取OAuth token失败，尝试不同参数...')
      const altResult = await tryDifferentParams(walletIndex)
      
      if (!altResult.success) {
        console.log('❌ 所有尝试都失败了')
        return
      }
      
      result.oauth_token = altResult.oauth_token
      result.auth_url = altResult.auth_url
      result.success = true
    }
    
    console.log(`\n✅ 成功获取OAuth token: ${result.oauth_token}`)
    
    // 2. 更新CSV文件
    const fs = require('fs')
    const csvContent = fs.readFileSync('./twitter-bindings-example.csv', 'utf8')
    const lines = csvContent.trim().split('\n')
    const [, twitterAuthToken] = lines[1].split(',')
    
    const newCsvContent = `walletIndex,twitterAuthToken,oauthToken\n${walletIndex},${twitterAuthToken},${result.oauth_token}`
    fs.writeFileSync('./twitter-bindings-example.csv', newCsvContent)
    
    console.log('✅ 已更新CSV文件')
    
    // 3. 测试Twitter绑定
    console.log('\n🚀 开始测试Twitter绑定...')
    
    const { bindTwitter } = require('./tasks/twitter-bind')
    
    const bindResult = await bindTwitter(
      walletIndex,
      twitterAuthToken.trim(),
      result.oauth_token,
      true // 处理回调
    )
    
    if (bindResult.success) {
      console.log('\n🎉 Twitter绑定测试成功!')
      console.log(`   OAuth Verifier: ${bindResult.data.oauthVerifier?.substring(0, 15)}...`)
      console.log(`   Authenticity Token: ${bindResult.data.authenticityToken?.substring(0, 15)}...`)
      console.log(`   回调处理: ${bindResult.data.callback?.success ? '成功' : '失败'}`)
      
      if (bindResult.data.callback?.success) {
        console.log(`   最终URL: ${bindResult.data.callback.finalUrl}`)
      }
    } else {
      console.log('\n❌ Twitter绑定测试失败')
      console.log(`   错误: ${bindResult.error}`)
    }
    
    return bindResult
    
  } catch (error) {
    console.error('更新和测试过程中发生错误:', error.message)
  }
}

/**
 * 显示使用说明
 */
function showUsage() {
  console.log('=== 正确的OAuth Token获取工具 ===\n')
  
  console.log('发现的正确API端点:')
  console.log('  POST https://api-theoriq.bonusblock.io/api/auth/social-link')
  console.log('  参数: { platform: "twitter" }\n')
  
  console.log('响应格式:')
  console.log('  {')
  console.log('    "errors": [],')
  console.log('    "payload": "https://api.twitter.com/oauth/authorize?oauth_token=TOKEN",')
  console.log('    "success": true,')
  console.log('    "now": "2025-07-18T15:12:02.138Z"')
  console.log('  }\n')
  
  console.log('使用方法:')
  console.log('  node get-correct-oauth-token.js                # 获取并测试钱包1')
  console.log('  node get-correct-oauth-token.js 2             # 获取并测试指定钱包')
  console.log('  node get-correct-oauth-token.js test          # 仅测试参数')
  console.log('  node get-correct-oauth-token.js help          # 显示帮助\n')
}

// 主程序
if (require.main === module) {
  const args = process.argv.slice(2)
  
  if (args[0] === 'help' || args[0] === '-h') {
    showUsage()
  } else if (args[0] === 'test') {
    tryDifferentParams(1).then(result => {
      console.log('测试结果:', result)
    })
  } else {
    const walletIndex = args[0] ? parseInt(args[0]) : 1
    updateAndTest(walletIndex)
  }
}

module.exports = { getCorrectTwitterOAuthToken, updateAndTest }
