const theoriqManager = require('./TheoriqManager')
const logger = require('./utils/logger')

/**
 * 从Theoriq API获取Twitter OAuth token
 * @param {number} walletIndex 钱包索引
 * @returns {Object} 包含oauth_token的结果
 */
async function getTwitterOAuthToken(walletIndex) {
  try {
    logger.info(`获取Twitter OAuth token - 钱包 ${walletIndex}`)
    
    // 1. 确保钱包已登录
    let token = theoriqManager.getWalletToken(walletIndex)
    if (!token) {
      console.log(`钱包 ${walletIndex} 未登录，正在登录...`)
      const loginResult = await theoriqManager.loginWallet(walletIndex)
      if (!loginResult.success) {
        throw new Error(`钱包登录失败: ${loginResult.error}`)
      }
      token = theoriqManager.getWalletToken(walletIndex)
    }
    
    console.log(`✅ 钱包 ${walletIndex} 已登录`)
    
    // 2. 请求Twitter OAuth token
    const result = await theoriqManager.sendAuthRequest(
      walletIndex,
      'POST',
      'https://api-theoriq.bonusblock.io/api/social/twitter/request-token',
      {}
    )
    
    if (result && result.oauth_token) {
      logger.success(`获取Twitter OAuth token成功 - 钱包 ${walletIndex}`, {
        oauth_token: result.oauth_token
      })
      
      return {
        success: true,
        oauth_token: result.oauth_token,
        oauth_token_secret: result.oauth_token_secret
      }
    } else {
      throw new Error(`API返回格式错误: ${JSON.stringify(result)}`)
    }
    
  } catch (error) {
    logger.error(`获取Twitter OAuth token失败 - 钱包 ${walletIndex}`, error.message)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 为所有钱包获取OAuth token
 */
async function getAllOAuthTokens() {
  try {
    const walletManager = require('./utils/WalletManager')
    const walletCount = walletManager.getWalletCount()
    
    console.log(`=== 获取所有钱包的Twitter OAuth Token ===`)
    console.log(`找到 ${walletCount} 个钱包\n`)
    
    const results = []
    
    for (let i = 1; i <= walletCount; i++) {
      console.log(`\n--- 处理钱包 ${i} ---`)
      
      const result = await getTwitterOAuthToken(i)
      results.push({
        walletIndex: i,
        ...result
      })
      
      if (result.success) {
        console.log(`✅ 钱包 ${i}: ${result.oauth_token}`)
      } else {
        console.log(`❌ 钱包 ${i}: ${result.error}`)
      }
      
      // 添加延迟避免请求过快
      if (i < walletCount) {
        console.log('等待2秒后继续...')
        await new Promise(resolve => setTimeout(resolve, 2000))
      }
    }
    
    // 显示总结
    console.log('\n=== 获取结果总结 ===')
    const successful = results.filter(r => r.success)
    const failed = results.filter(r => !r.success)
    
    console.log(`成功: ${successful.length} 个`)
    console.log(`失败: ${failed.length} 个`)
    
    if (successful.length > 0) {
      console.log('\n成功的OAuth tokens:')
      successful.forEach(r => {
        console.log(`  钱包 ${r.walletIndex}: ${r.oauth_token}`)
      })
    }
    
    if (failed.length > 0) {
      console.log('\n失败详情:')
      failed.forEach(r => {
        console.log(`  钱包 ${r.walletIndex}: ${r.error}`)
      })
    }
    
    return results
    
  } catch (error) {
    console.error('获取OAuth tokens过程中发生错误:', error.message)
    return []
  }
}

/**
 * 更新CSV文件中的OAuth token
 */
async function updateCSVWithOAuthTokens() {
  try {
    const fs = require('fs')
    const path = require('path')
    
    console.log('=== 更新CSV文件中的OAuth Token ===\n')
    
    // 1. 获取所有OAuth tokens
    const results = await getAllOAuthTokens()
    const successful = results.filter(r => r.success)
    
    if (successful.length === 0) {
      console.log('❌ 没有成功获取到任何OAuth token')
      return
    }
    
    // 2. 读取现有CSV文件
    const csvPath = path.join(__dirname, 'twitter-bindings-example.csv')
    let csvContent = ''
    
    try {
      csvContent = fs.readFileSync(csvPath, 'utf8')
    } catch (error) {
      console.log('CSV文件不存在，将创建新文件')
    }
    
    // 3. 解析现有数据
    const lines = csvContent.trim().split('\n').filter(line => line.trim())
    const existingData = new Map()
    
    if (lines.length > 1) {
      for (let i = 1; i < lines.length; i++) {
        const [walletIndex, twitterAuthToken] = lines[i].split(',')
        if (walletIndex && twitterAuthToken) {
          existingData.set(parseInt(walletIndex), twitterAuthToken.trim())
        }
      }
    }
    
    // 4. 构建新的CSV内容
    let newCsvContent = 'walletIndex,twitterAuthToken,oauthToken\n'
    
    successful.forEach(result => {
      const walletIndex = result.walletIndex
      const twitterAuthToken = existingData.get(walletIndex) || 'your_twitter_auth_token_here'
      const oauthToken = result.oauth_token
      
      newCsvContent += `${walletIndex},${twitterAuthToken},${oauthToken}\n`
    })
    
    // 5. 写入文件
    fs.writeFileSync(csvPath, newCsvContent)
    
    console.log(`✅ 已更新 ${successful.length} 个钱包的OAuth token到 ${csvPath}`)
    console.log('\n⚠️  请记得在CSV文件中设置正确的Twitter auth_token!')
    
  } catch (error) {
    console.error('更新CSV文件失败:', error.message)
  }
}

/**
 * 显示使用说明
 */
function showUsage() {
  console.log('=== OAuth Token 获取工具使用说明 ===\n')
  
  console.log('功能:')
  console.log('  - 从Theoriq API获取Twitter OAuth token')
  console.log('  - 自动更新CSV文件中的OAuth token\n')
  
  console.log('使用方法:')
  console.log('  node get-oauth-token.js                    # 获取所有钱包的OAuth token')
  console.log('  node get-oauth-token.js 1                 # 获取指定钱包的OAuth token')
  console.log('  node get-oauth-token.js update            # 获取并更新CSV文件')
  console.log('  node get-oauth-token.js help              # 显示帮助\n')
  
  console.log('注意事项:')
  console.log('  1. 确保钱包已在 wallets.csv 中配置')
  console.log('  2. 程序会自动登录钱包（如果未登录）')
  console.log('  3. OAuth token有时效性，需要及时使用')
  console.log('  4. 获取后请在CSV文件中设置正确的Twitter auth_token\n')
}

// 主程序
if (require.main === module) {
  const args = process.argv.slice(2)
  
  if (args[0] === 'help' || args[0] === '-h') {
    showUsage()
  } else if (args[0] === 'update') {
    updateCSVWithOAuthTokens()
  } else if (args[0] && !isNaN(parseInt(args[0]))) {
    // 获取指定钱包的OAuth token
    const walletIndex = parseInt(args[0])
    getTwitterOAuthToken(walletIndex).then(result => {
      if (result.success) {
        console.log(`✅ 钱包 ${walletIndex} OAuth token: ${result.oauth_token}`)
      } else {
        console.log(`❌ 钱包 ${walletIndex} 获取失败: ${result.error}`)
      }
    })
  } else {
    getAllOAuthTokens()
  }
}

module.exports = { getTwitterOAuthToken, getAllOAuthTokens, updateCSVWithOAuthTokens }
