[2025-06-14T12:58:36.838Z] [TASK] 开始任务: 批量登录 1 个钱包
[2025-06-14T12:58:36.854Z] [INFO] 开始登录钱包 1 address: ******************************************
[2025-06-14T12:58:36.855Z] [INFO] 获取认证票据 nonce: e17c5848-7024-4998-b66d-21dca54cad54
[2025-06-14T12:58:42.077Z] [SUCCESS] 获取认证票据成功 payload: BONUSBLOCK_20250614_2WWz
[2025-06-14T12:58:42.120Z] [INFO] 执行以太坊认证 nonce: e17c5848-7024-4998-b66d-21dca54cad54, blockchainName: okx
[2025-06-14T12:58:42.791Z] [SUCCESS] 以太坊认证成功 userId: eURZXd8Q, expiresOn: 2025-06-14T18:58:40.266Z
[2025-06-14T12:58:42.793Z] [SUCCESS] 钱包 1 登录成功 userId: eURZXd8Q, walletAddress: ******************************************
[2025-06-14T12:58:42.808Z] [TASK] 任务完成: 批量登录
