[2025-07-18T15:00:03.996Z] [INFO] 加载了 0 个有效token
[2025-07-18T15:01:04.045Z] [INFO] 加载了 0 个有效token
[2025-07-18T15:01:04.046Z] [TASK] 开始任务: 批量登录 1 个钱包
[2025-07-18T15:01:04.046Z] [INFO] 开始登录钱包 1 address: ******************************************
[2025-07-18T15:01:04.046Z] [INFO] 获取认证票据 nonce: 576a1075-17fc-49b2-9efe-cf596a393df9
[2025-07-18T15:01:05.074Z] [SUCCESS] 获取认证票据成功 payload: BONUSBLOCK_20250718_GYI1
[2025-07-18T15:01:05.111Z] [INFO] 执行以太坊认证 nonce: 576a1075-17fc-49b2-9efe-cf596a393df9, blockchainName: okx
[2025-07-18T15:01:05.455Z] [SUCCESS] 以太坊认证成功 userId: xBQgm5bB, expiresOn: 2025-07-18T21:01:09.579Z
[2025-07-18T15:01:05.457Z] [SUCCESS] 钱包 1 登录成功 userId: xBQgm5bB, walletAddress: ******************************************
[2025-07-18T15:01:05.458Z] [TASK] 任务完成: 批量登录
[2025-07-18T15:01:10.791Z] [INFO] 加载了 1 个有效token
[2025-07-18T15:01:10.791Z] [INFO] 获取Twitter OAuth token - 钱包 1
[2025-07-18T15:01:11.741Z] [WARN] 请求失败，第1次重试: Request failed with status code 400
[2025-07-18T15:01:14.068Z] [WARN] 请求失败，第2次重试: Request failed with status code 400
[2025-07-18T15:01:18.392Z] [ERROR] 获取Twitter OAuth token失败 - 钱包 1 Request failed with status code 400
[2025-07-18T15:02:11.798Z] [INFO] 加载了 1 个有效token
[2025-07-18T15:02:20.660Z] [INFO] 加载了 1 个有效token
[2025-07-18T15:02:20.662Z] [INFO] 开始Twitter绑定任务 - 钱包 1
[2025-07-18T15:02:20.662Z] [INFO] 开始Twitter OAuth完整流程 oauthToken: UyK0UAAAAAABoeyUAAABl26malA, handleCallback: true
[2025-07-18T15:02:20.662Z] [INFO] 获取Twitter authenticity_token oauthToken: UyK0UAAAAAABoeyUAAABl26malA
[2025-07-18T15:02:21.199Z] [ERROR] 获取authenticity_token失败 - 未找到token
[2025-07-18T15:02:21.200Z] [ERROR] Twitter绑定失败 - 钱包 1 Twitter OAuth失败: OAuth授权失败
[2025-07-18T15:03:46.626Z] [INFO] 获取Twitter authenticity_token oauthToken: UyK0UAAAAAABoeyUAAABl26malA
[2025-07-18T15:03:47.211Z] [ERROR] 获取authenticity_token失败 - 未找到token
[2025-07-18T15:06:25.997Z] [INFO] 获取Twitter authenticity_token oauthToken: UyK0UAAAAAABoeyUAAABl26malA
[2025-07-18T15:06:26.600Z] [SUCCESS] 获取authenticity_token成功 token: e02453f378...
[2025-07-18T15:06:26.607Z] [INFO] 获取Twitter authenticity_token oauthToken: UyK0UAAAAAABoeyUAAABl26malA
[2025-07-18T15:06:26.982Z] [SUCCESS] 获取authenticity_token成功 token: 53e30840b8...
[2025-07-18T15:06:26.982Z] [INFO] 执行Twitter OAuth授权 oauthToken: UyK0UAAAAAABoeyUAAABl26malA
[2025-07-18T15:06:27.506Z] [ERROR] Twitter OAuth授权失败 response.data.includes is not a function
[2025-07-18T15:06:53.920Z] [INFO] 获取Twitter authenticity_token oauthToken: UyK0UAAAAAABoeyUAAABl26malA
[2025-07-18T15:06:54.477Z] [SUCCESS] 获取authenticity_token成功 token: f05ece96ef...
[2025-07-18T15:06:54.478Z] [INFO] 获取Twitter authenticity_token oauthToken: UyK0UAAAAAABoeyUAAABl26malA
[2025-07-18T15:06:54.850Z] [SUCCESS] 获取authenticity_token成功 token: d14bff1d55...
[2025-07-18T15:06:54.850Z] [INFO] 执行Twitter OAuth授权 oauthToken: UyK0UAAAAAABoeyUAAABl26malA
[2025-07-18T15:06:55.392Z] [ERROR] Twitter OAuth授权失败 - 未找到oauth_verifier
[2025-07-18T15:07:02.499Z] [INFO] 加载了 1 个有效token
[2025-07-18T15:07:02.499Z] [INFO] 获取Twitter OAuth token - 钱包 1
[2025-07-18T15:07:03.150Z] [WARN] 请求失败，第1次重试: Request failed with status code 400
[2025-07-18T15:07:05.507Z] [WARN] 请求失败，第2次重试: Request failed with status code 400
[2025-07-18T15:07:09.854Z] [ERROR] 获取Twitter OAuth token失败 - 钱包 1 Request failed with status code 400
[2025-07-18T15:07:57.032Z] [INFO] 加载了 1 个有效token
[2025-07-18T15:08:15.276Z] [INFO] 加载了 1 个有效token
[2025-07-18T15:08:57.658Z] [INFO] 加载了 1 个有效token
[2025-07-18T15:08:57.663Z] [INFO] 开始Twitter绑定任务 - 钱包 1
[2025-07-18T15:08:57.663Z] [INFO] 开始Twitter OAuth完整流程 oauthToken: UyK0UAAAAAABoeyUAAABl26malA, handleCallback: false
[2025-07-18T15:08:57.664Z] [INFO] 获取Twitter authenticity_token oauthToken: UyK0UAAAAAABoeyUAAABl26malA
[2025-07-18T15:08:58.228Z] [SUCCESS] 获取authenticity_token成功 token: f81792cc5d...
[2025-07-18T15:08:58.256Z] [INFO] 执行Twitter OAuth授权 oauthToken: UyK0UAAAAAABoeyUAAABl26malA
[2025-07-18T15:08:58.822Z] [ERROR] Twitter OAuth授权失败 - 未找到oauth_verifier
[2025-07-18T15:08:58.822Z] [ERROR] Twitter绑定失败 - 钱包 1 Twitter OAuth失败: OAuth授权失败
[2025-07-18T15:09:12.139Z] [INFO] 获取Twitter authenticity_token oauthToken: UyK0UAAAAAABoeyUAAABl26malA
[2025-07-18T15:09:12.672Z] [SUCCESS] 获取authenticity_token成功 token: ec9d59423c...
[2025-07-18T15:09:12.673Z] [INFO] 获取Twitter authenticity_token oauthToken: UyK0UAAAAAABoeyUAAABl26malA
[2025-07-18T15:09:13.108Z] [SUCCESS] 获取authenticity_token成功 token: c7f5e26dc6...
[2025-07-18T15:09:13.157Z] [INFO] 执行Twitter OAuth授权 oauthToken: UyK0UAAAAAABoeyUAAABl26malA
[2025-07-18T15:09:13.699Z] [ERROR] Twitter OAuth授权失败 - 未找到oauth_verifier
[2025-07-18T15:09:15.702Z] [INFO] 获取Twitter authenticity_token oauthToken: UyK0UAAAAAABoeyUAAABl26malB
[2025-07-18T15:09:16.082Z] [SUCCESS] 获取authenticity_token成功 token: 15bf13a34a...
[2025-07-18T15:09:16.082Z] [INFO] 获取Twitter authenticity_token oauthToken: UyK0UAAAAAABoeyUAAABl26malB
[2025-07-18T15:09:16.449Z] [SUCCESS] 获取authenticity_token成功 token: 48f3be94bd...
[2025-07-18T15:09:16.450Z] [INFO] 执行Twitter OAuth授权 oauthToken: UyK0UAAAAAABoeyUAAABl26malB
[2025-07-18T15:09:17.036Z] [ERROR] Twitter OAuth授权失败 - 未找到oauth_verifier
[2025-07-18T15:09:19.040Z] [INFO] 获取Twitter authenticity_token oauthToken: UyK0UAAAAAABoeyUAAABl26malC
[2025-07-18T15:09:19.409Z] [SUCCESS] 获取authenticity_token成功 token: 31ff0f2be3...
[2025-07-18T15:09:19.410Z] [INFO] 获取Twitter authenticity_token oauthToken: UyK0UAAAAAABoeyUAAABl26malC
[2025-07-18T15:09:19.787Z] [SUCCESS] 获取authenticity_token成功 token: 3dcd93e51b...
[2025-07-18T15:09:19.788Z] [INFO] 执行Twitter OAuth授权 oauthToken: UyK0UAAAAAABoeyUAAABl26malC
[2025-07-18T15:09:20.316Z] [ERROR] Twitter OAuth授权失败 - 未找到oauth_verifier
[2025-07-18T15:09:22.321Z] [INFO] 获取Twitter authenticity_token oauthToken: test_oauth_token_123456789
[2025-07-18T15:09:22.706Z] [SUCCESS] 获取authenticity_token成功 token: ac7c5b2ddb...
[2025-07-18T15:09:22.707Z] [INFO] 获取Twitter authenticity_token oauthToken: test_oauth_token_123456789
[2025-07-18T15:09:23.060Z] [SUCCESS] 获取authenticity_token成功 token: ca97b7a3f0...
[2025-07-18T15:09:23.060Z] [INFO] 执行Twitter OAuth授权 oauthToken: test_oauth_token_123456789
[2025-07-18T15:09:23.660Z] [ERROR] Twitter OAuth授权失败 - 未找到oauth_verifier
[2025-07-18T15:10:17.661Z] [INFO] 获取Twitter authenticity_token oauthToken: UyK0UAAAAAABoeyUAAABl26malA
[2025-07-18T15:10:18.343Z] [SUCCESS] 获取authenticity_token成功 token: 86f0bf9034...
[2025-07-18T15:10:18.344Z] [INFO] 获取Twitter authenticity_token oauthToken: UyK0UAAAAAABoeyUAAABl26malA
[2025-07-18T15:10:18.720Z] [SUCCESS] 获取authenticity_token成功 token: 38239ba1bb...
[2025-07-18T15:10:18.721Z] [INFO] 模拟Twitter OAuth授权 oauthToken: UyK0UAAAAAABoeyUAAABl26malA
[2025-07-18T15:10:18.721Z] [SUCCESS] 模拟Twitter OAuth授权成功 verifier: mock_oauth...
[2025-07-18T15:10:18.722Z] [INFO] 模拟Twitter OAuth回调 oauthToken: UyK0UAAAAAABoeyUAAABl26malA, verifier: mock_oauth...
[2025-07-18T15:10:18.723Z] [SUCCESS] 模拟Twitter OAuth回调处理成功 status: 200, redirected: https://quests.theoriq.ai/quests
[2025-07-18T15:10:18.724Z] [INFO] 开始Twitter OAuth完整流程 oauthToken: UyK0UAAAAAABoeyUAAABl26malA, handleCallback: true
[2025-07-18T15:10:18.725Z] [INFO] 获取Twitter authenticity_token oauthToken: UyK0UAAAAAABoeyUAAABl26malA
[2025-07-18T15:10:19.081Z] [SUCCESS] 获取authenticity_token成功 token: 0f72523493...
[2025-07-18T15:10:19.082Z] [INFO] 模拟Twitter OAuth授权 oauthToken: UyK0UAAAAAABoeyUAAABl26malA
[2025-07-18T15:10:19.084Z] [SUCCESS] 模拟Twitter OAuth授权成功 verifier: mock_oauth...
[2025-07-18T15:10:19.086Z] [INFO] 模拟Twitter OAuth回调 oauthToken: UyK0UAAAAAABoeyUAAABl26malA, verifier: mock_oauth...
[2025-07-18T15:10:19.086Z] [SUCCESS] 模拟Twitter OAuth回调处理成功 status: 200, redirected: https://quests.theoriq.ai/quests
[2025-07-18T15:10:27.678Z] [INFO] 加载了 1 个有效token
[2025-07-18T15:10:27.678Z] [INFO] 开始Twitter绑定任务 - 钱包 1
[2025-07-18T15:10:27.678Z] [INFO] 开始Twitter OAuth完整流程 oauthToken: UyK0UAAAAAABoeyUAAABl26malA, handleCallback: true
[2025-07-18T15:10:27.679Z] [INFO] 获取Twitter authenticity_token oauthToken: UyK0UAAAAAABoeyUAAABl26malA
[2025-07-18T15:10:28.185Z] [SUCCESS] 获取authenticity_token成功 token: fc505539df...
[2025-07-18T15:10:28.185Z] [INFO] 模拟Twitter OAuth授权 oauthToken: UyK0UAAAAAABoeyUAAABl26malA
[2025-07-18T15:10:28.185Z] [SUCCESS] 模拟Twitter OAuth授权成功 verifier: mock_oauth...
[2025-07-18T15:10:28.186Z] [INFO] 模拟Twitter OAuth回调 oauthToken: UyK0UAAAAAABoeyUAAABl26malA, verifier: mock_oauth...
[2025-07-18T15:10:28.186Z] [SUCCESS] 模拟Twitter OAuth回调处理成功 status: 200, redirected: https://quests.theoriq.ai/quests
[2025-07-18T15:10:28.187Z] [SUCCESS] Twitter OAuth成功 - 钱包 1 verifier: mock_oauth..., callbackHandled: true, callbackSuccess: true
[2025-07-18T15:14:24.166Z] [INFO] 加载了 1 个有效token
[2025-07-18T15:14:24.166Z] [INFO] 获取Twitter OAuth token - 钱包 1
[2025-07-18T15:14:24.658Z] [WARN] 请求失败，第1次重试: Request failed with status code 400
[2025-07-18T15:14:27.155Z] [WARN] 请求失败，第2次重试: Request failed with status code 400
[2025-07-18T15:14:31.492Z] [ERROR] 获取Twitter OAuth token失败 - 钱包 1 Request failed with status code 400
[2025-07-18T15:14:31.821Z] [WARN] 请求失败，第1次重试: Request failed with status code 400
[2025-07-18T15:14:34.154Z] [WARN] 请求失败，第2次重试: Request failed with status code 400
[2025-07-18T15:14:38.863Z] [WARN] 请求失败，第1次重试: Request failed with status code 400
[2025-07-18T15:14:41.221Z] [WARN] 请求失败，第2次重试: Request failed with status code 400
[2025-07-18T15:14:45.879Z] [WARN] 请求失败，第1次重试: Request failed with status code 400
[2025-07-18T15:14:48.207Z] [WARN] 请求失败，第2次重试: Request failed with status code 400
[2025-07-18T15:14:52.860Z] [WARN] 请求失败，第1次重试: Request failed with status code 400
[2025-07-18T15:14:55.191Z] [WARN] 请求失败，第2次重试: Request failed with status code 400
[2025-07-18T15:14:59.855Z] [WARN] 请求失败，第1次重试: Request failed with status code 400
[2025-07-18T15:15:02.187Z] [WARN] 请求失败，第2次重试: Request failed with status code 400
[2025-07-18T15:15:06.840Z] [WARN] 请求失败，第1次重试: Request failed with status code 400
[2025-07-18T15:15:09.182Z] [WARN] 请求失败，第2次重试: Request failed with status code 400
[2025-07-18T15:16:59.465Z] [INFO] 加载了 1 个有效token
[2025-07-18T15:16:59.467Z] [INFO] 获取Twitter OAuth token - 钱包 1
[2025-07-18T15:17:00.752Z] [SUCCESS] 获取Twitter OAuth token成功 - 钱包 1 oauth_token: ICJXFwAAAAABoeyUAAABmB4cQC8, auth_url: https://api.twitter.com/oauth/authorize?oauth_token=ICJXFwAAAAABoeyUAAABmB4cQC8
[2025-07-18T15:17:00.760Z] [INFO] 开始Twitter绑定任务 - 钱包 1
[2025-07-18T15:17:00.761Z] [INFO] 开始Twitter OAuth完整流程 oauthToken: ICJXFwAAAAABoeyUAAABmB4cQC8, handleCallback: true
[2025-07-18T15:17:00.761Z] [INFO] 获取Twitter authenticity_token oauthToken: ICJXFwAAAAABoeyUAAABmB4cQC8
[2025-07-18T15:17:01.614Z] [SUCCESS] 获取authenticity_token成功 token: 5b709610dc...
[2025-07-18T15:17:01.614Z] [INFO] 执行Twitter OAuth授权 oauthToken: ICJXFwAAAAABoeyUAAABmB4cQC8
[2025-07-18T15:17:02.172Z] [ERROR] Twitter OAuth授权失败 - 未找到oauth_verifier
[2025-07-18T15:17:02.172Z] [ERROR] Twitter绑定失败 - 钱包 1 Twitter OAuth失败: OAuth授权失败
[2025-07-18T15:17:58.609Z] [INFO] 获取Twitter authenticity_token oauthToken: ICJXFwAAAAABoeyUAAABmB4cQC8
[2025-07-18T15:17:59.280Z] [SUCCESS] 获取authenticity_token成功 token: f58d0f32e5...
[2025-07-18T15:18:14.539Z] [INFO] 获取Twitter authenticity_token oauthToken: ICJXFwAAAAABoeyUAAABmB4cQC8
[2025-07-18T15:18:15.252Z] [SUCCESS] 获取authenticity_token成功 token: 1fd64320ea...
[2025-07-18T15:19:19.875Z] [INFO] 获取Twitter authenticity_token oauthToken: ICJXFwAAAAABoeyUAAABmB4cQC8
[2025-07-18T15:19:20.554Z] [INFO] 获取CSRF token成功 token: 5840c9c896...
[2025-07-18T15:19:20.555Z] [SUCCESS] 获取authenticity_token成功 token: 8030b38148...
[2025-07-18T15:20:40.674Z] [INFO] 加载了 1 个有效token
[2025-07-18T15:20:40.675Z] [INFO] 开始Twitter绑定任务 - 钱包 1
[2025-07-18T15:20:40.675Z] [INFO] 开始Twitter OAuth完整流程 oauthToken: ICJXFwAAAAABoeyUAAABmB4cQC8, handleCallback: true
[2025-07-18T15:20:40.676Z] [INFO] 获取Twitter authenticity_token oauthToken: ICJXFwAAAAABoeyUAAABmB4cQC8
[2025-07-18T15:20:41.408Z] [INFO] 获取CSRF token成功 token: 54b8f3d12f...
[2025-07-18T15:20:41.408Z] [SUCCESS] 获取authenticity_token成功 token: df03a152eb...
[2025-07-18T15:20:41.408Z] [INFO] 模拟Twitter OAuth授权 oauthToken: ICJXFwAAAAABoeyUAAABmB4cQC8
[2025-07-18T15:20:41.408Z] [SUCCESS] 模拟Twitter OAuth授权成功 verifier: mock_oauth...
[2025-07-18T15:20:41.408Z] [INFO] 模拟Twitter OAuth回调 oauthToken: ICJXFwAAAAABoeyUAAABmB4cQC8, verifier: mock_oauth...
[2025-07-18T15:20:41.409Z] [SUCCESS] 模拟Twitter OAuth回调处理成功 status: 200, redirected: https://quests.theoriq.ai/quests
[2025-07-18T15:20:41.409Z] [SUCCESS] Twitter OAuth成功 - 钱包 1 verifier: mock_oauth..., callbackHandled: true, callbackSuccess: true
[2025-07-18T16:04:28.219Z] [INFO] 获取Twitter tokens oauthToken: test_oauth_token_123
[2025-07-18T16:04:28.858Z] [INFO] 获取CSRF token成功 token: be8d01993a...
[2025-07-18T16:04:28.859Z] [SUCCESS] 获取authenticity_token成功 token: 3eb7244065...
[2025-07-18T16:04:30.863Z] [INFO] 获取Twitter tokens oauthToken: test_oauth_token_123
[2025-07-18T16:04:31.240Z] [INFO] 获取CSRF token成功 token: 66b7bbd000...
[2025-07-18T16:04:31.241Z] [SUCCESS] 获取authenticity_token成功 token: c9c0facdcb...
[2025-07-18T16:04:31.241Z] [INFO] 执行Twitter OAuth授权 oauthToken: test_oauth_token_123
[2025-07-18T16:04:31.242Z] [INFO] 尝试标准OAuth授权
[2025-07-18T16:04:31.774Z] [INFO] 尝试带授权参数的OAuth
[2025-07-18T16:04:34.287Z] [INFO] 获取Twitter tokens oauthToken: test_oauth_token_123
[2025-07-18T16:04:34.651Z] [INFO] 获取CSRF token成功 token: dc464c7f11...
[2025-07-18T16:04:34.652Z] [SUCCESS] 获取authenticity_token成功 token: a8b4f7efcc...
[2025-07-18T16:04:34.652Z] [INFO] 执行Twitter OAuth授权 oauthToken: test_oauth_token_123
[2025-07-18T16:04:34.652Z] [INFO] 尝试标准OAuth授权
[2025-07-18T16:04:35.186Z] [INFO] 尝试带授权参数的OAuth
[2025-07-18T16:04:37.723Z] [INFO] 获取Twitter tokens oauthToken: test_oauth_token_123
[2025-07-18T16:04:38.106Z] [INFO] 获取CSRF token成功 token: b738e82719...
[2025-07-18T16:04:38.106Z] [SUCCESS] 获取authenticity_token成功 token: fb6f3af26b...
[2025-07-18T16:08:53.024Z] [INFO] 获取Twitter tokens oauthToken: yT2QzAAAAAABpaBwAAABmB5LtcY
[2025-07-18T16:08:53.826Z] [INFO] 获取CSRF token成功 token: bc7ee25107...
[2025-07-18T16:08:53.826Z] [SUCCESS] 获取authenticity_token成功 token: 4b82b5bae5...
[2025-07-18T16:08:53.827Z] [INFO] 执行Twitter OAuth授权 oauthToken: yT2QzAAAAAABpaBwAAABmB5LtcY
[2025-07-18T16:08:53.827Z] [INFO] 尝试标准OAuth授权
[2025-07-18T16:08:54.371Z] [INFO] 尝试带授权参数的OAuth
