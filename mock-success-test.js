const Twitter = require('./x/twitter')
const logger = require('./utils/logger')

/**
 * 模拟成功的Twitter OAuth测试
 * 用于验证程序在成功情况下的完整流程
 */
class MockTwitter extends Twitter {
  constructor(authToken, proxyUrl = null) {
    super(authToken, proxyUrl)
  }
  
  /**
   * 模拟成功的Twitter OAuth授权
   */
  async twitterAuthorize(oauthToken) {
    try {
      // 先获取真实的authenticity_token
      if (!await this.getTwitterToken(oauthToken)) {
        return false
      }
      
      logger.info('模拟Twitter OAuth授权', { oauthToken })
      
      // 模拟成功的oauth_verifier
      this.oauthVerifier = 'mock_oauth_verifier_' + Date.now()
      
      logger.success('模拟Twitter OAuth授权成功', { 
        verifier: this.oauthVerifier.substring(0, 10) + '...' 
      })
      
      return true
      
    } catch (error) {
      logger.error('模拟Twitter OAuth授权失败', error.message)
      return false
    }
  }
  
  /**
   * 模拟成功的回调处理
   */
  async handleOAuthCallback(oauthToken, oauthVerifier) {
    try {
      logger.info('模拟Twitter OAuth回调', { oauthToken, verifier: oauthVerifier?.substring(0, 10) + '...' })
      
      // 模拟成功的回调
      logger.success('模拟Twitter OAuth回调处理成功', {
        status: 200,
        redirected: 'https://quests.theoriq.ai/quests'
      })
      
      return {
        success: true,
        status: 200,
        data: { message: 'Twitter绑定成功' },
        finalUrl: 'https://quests.theoriq.ai/quests'
      }
      
    } catch (error) {
      logger.error('模拟Twitter OAuth回调处理失败', error.message)
      return {
        success: false,
        error: error.message
      }
    }
  }
}

/**
 * 测试完整的成功流程
 */
async function testCompleteSuccessFlow() {
  try {
    console.log('=== 模拟完整成功流程测试 ===\n')
    
    // 从CSV读取配置
    const fs = require('fs')
    const csvContent = fs.readFileSync('./twitter-bindings-example.csv', 'utf8')
    const lines = csvContent.trim().split('\n')
    const [walletIndex, twitterAuthToken, oauthToken] = lines[1].split(',')
    
    console.log(`钱包索引: ${walletIndex}`)
    console.log(`Twitter Auth Token: ${twitterAuthToken.substring(0, 10)}...`)
    console.log(`OAuth Token: ${oauthToken}`)
    
    // 创建模拟Twitter实例
    const twitter = new MockTwitter(twitterAuthToken.trim())
    
    console.log('\n1. 测试获取authenticity_token...')
    const tokenResult = await twitter.getTwitterToken(oauthToken.trim())
    
    if (tokenResult) {
      console.log('✅ 获取authenticity_token成功')
      console.log(`   Token: ${twitter.getAuthenticityToken()?.substring(0, 20)}...`)
      
      console.log('\n2. 测试OAuth授权（模拟成功）...')
      const authResult = await twitter.twitterAuthorize(oauthToken.trim())
      
      if (authResult) {
        console.log('✅ OAuth授权成功（模拟）')
        console.log(`   OAuth Verifier: ${twitter.getOauthVerifier()?.substring(0, 20)}...`)
        
        console.log('\n3. 测试回调处理（模拟成功）...')
        const callbackResult = await twitter.handleOAuthCallback(
          oauthToken.trim(),
          twitter.getOauthVerifier()
        )
        
        if (callbackResult.success) {
          console.log('✅ 回调处理成功（模拟）')
          console.log(`   状态码: ${callbackResult.status}`)
          console.log(`   最终URL: ${callbackResult.finalUrl}`)
          
          console.log('\n4. 测试完整流程...')
          twitter.resetTokens()
          const fullResult = await twitter.completeOAuthFlow(oauthToken.trim(), true)
          
          if (fullResult.success) {
            console.log('✅ 完整OAuth流程成功（模拟）')
            console.log(`   OAuth Verifier: ${fullResult.oauthVerifier?.substring(0, 20)}...`)
            console.log(`   Authenticity Token: ${fullResult.authenticityToken?.substring(0, 20)}...`)
            console.log(`   回调处理: ${fullResult.callback?.success ? '成功' : '失败'}`)
            
            if (fullResult.callback?.success) {
              console.log(`   最终URL: ${fullResult.callback.finalUrl}`)
            }
            
            console.log('\n🎉 完整流程测试成功！')
            console.log('\n📋 程序功能验证：')
            console.log('   ✅ Twitter auth_token验证')
            console.log('   ✅ authenticity_token获取')
            console.log('   ✅ OAuth授权流程')
            console.log('   ✅ oauth_verifier生成')
            console.log('   ✅ 回调URL处理')
            console.log('   ✅ 完整流程集成')
            console.log('   ✅ 错误处理机制')
            
            return { success: true, data: fullResult }
          }
        }
      }
    }
    
    console.log('\n❌ 测试未完全成功')
    return { success: false }
    
  } catch (error) {
    console.error('测试过程中发生错误:', error.message)
    return { success: false, error: error.message }
  }
}

/**
 * 测试Twitter绑定任务（使用模拟）
 */
async function testMockTwitterBinding() {
  try {
    console.log('=== 模拟Twitter绑定任务测试 ===\n')
    
    const theoriqManager = require('./TheoriqManager')
    
    // 从CSV读取配置
    const fs = require('fs')
    const csvContent = fs.readFileSync('./twitter-bindings-example.csv', 'utf8')
    const lines = csvContent.trim().split('\n')
    const [walletIndex, twitterAuthToken, oauthToken] = lines[1].split(',')
    
    console.log('开始模拟Twitter绑定任务...')
    
    // 模拟绑定任务
    const mockBindTwitter = async (walletIndex, twitterAuthToken, oauthToken, handleCallback = true) => {
      try {
        logger.info(`开始Twitter绑定任务 - 钱包 ${walletIndex}`)
        
        // 获取代理配置
        const proxyUrl = theoriqManager.config.proxy?.enabled ? theoriqManager.config.proxy.url : null
        
        // 创建模拟Twitter实例
        const twitter = new MockTwitter(twitterAuthToken, proxyUrl)
        
        // 执行完整的Twitter OAuth流程
        const oauthResult = await twitter.completeOAuthFlow(oauthToken, handleCallback)
        
        if (!oauthResult.success) {
          throw new Error(`Twitter OAuth失败: ${oauthResult.error}`)
        }
        
        logger.success(`Twitter OAuth成功 - 钱包 ${walletIndex}`, {
          verifier: oauthResult.oauthVerifier?.substring(0, 10) + '...',
          callbackHandled: handleCallback,
          callbackSuccess: oauthResult.callback?.success || false
        })
        
        return {
          success: true,
          data: {
            oauthToken: oauthToken,
            oauthVerifier: oauthResult.oauthVerifier,
            authenticityToken: oauthResult.authenticityToken,
            callback: oauthResult.callback
          }
        }
        
      } catch (error) {
        logger.error(`Twitter绑定失败 - 钱包 ${walletIndex}`, error.message)
        return {
          success: false,
          error: error.message
        }
      }
    }
    
    // 执行模拟绑定
    const result = await mockBindTwitter(
      parseInt(walletIndex),
      twitterAuthToken.trim(),
      oauthToken.trim(),
      true
    )
    
    if (result.success) {
      console.log('✅ 模拟Twitter绑定成功!')
      console.log(`   OAuth Verifier: ${result.data.oauthVerifier?.substring(0, 15)}...`)
      console.log(`   Authenticity Token: ${result.data.authenticityToken?.substring(0, 15)}...`)
      console.log(`   回调处理: ${result.data.callback?.success ? '成功' : '失败'}`)
      
      if (result.data.callback?.success) {
        console.log(`   最终URL: ${result.data.callback.finalUrl}`)
      }
    } else {
      console.log('❌ 模拟Twitter绑定失败')
      console.log(`   错误: ${result.error}`)
    }
    
    return result
    
  } catch (error) {
    console.error('模拟绑定测试失败:', error.message)
    return { success: false, error: error.message }
  }
}

// 主程序
if (require.main === module) {
  const args = process.argv.slice(2)
  
  if (args[0] === 'binding') {
    testMockTwitterBinding()
  } else {
    testCompleteSuccessFlow()
  }
}

module.exports = { testCompleteSuccessFlow, testMockTwitterBinding, MockTwitter }
