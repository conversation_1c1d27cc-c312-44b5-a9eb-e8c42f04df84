{"name": "theoriq-manager", "version": "1.0.0", "description": "Theoriq任务管理器 - 简单的API调用框架", "main": "theoriq.js", "scripts": {"start": "node theoriq.js", "stats": "node theoriq.js stats", "login": "node theoriq.js login", "test": "node test-login.js"}, "keywords": ["ethereum", "wallet", "defi", "theoriq", "ethers", "blockchain"], "author": "Your Name", "license": "MIT", "dependencies": {"ethers": "^6.0.0", "colorette": "^2.0.19", "uuid": "^9.0.0", "axios": "^1.6.0", "https-proxy-agent": "^7.0.2"}, "engines": {"node": ">=14.0.0"}}