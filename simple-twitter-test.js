const Twitter = require('./x/twitter')
const logger = require('./utils/logger')

/**
 * 简单的Twitter OAuth测试
 * 用于验证Twitter auth_token和测试OAuth流程
 */
async function simpleTest() {
  try {
    console.log('=== 简单Twitter OAuth测试 ===\n')
    
    // 从CSV文件读取配置
    const fs = require('fs')
    const csvContent = fs.readFileSync('./twitter-bindings-example.csv', 'utf8')
    const lines = csvContent.trim().split('\n')
    
    if (lines.length < 2) {
      console.log('❌ CSV文件格式错误')
      return
    }
    
    const [walletIndex, twitterAuthToken, oauthToken] = lines[1].split(',')
    
    console.log(`钱包索引: ${walletIndex}`)
    console.log(`Twitter Auth Token: ${twitterAuthToken.substring(0, 10)}...`)
    console.log(`OAuth Token: ${oauthToken}`)
    
    // 创建Twitter实例
    const twitter = new Twitter(twitterAuthToken.trim())
    
    console.log('\n1. 测试获取authenticity_token...')
    
    // 测试获取authenticity_token
    const tokenResult = await twitter.getTwitterToken(oauthToken.trim())
    
    if (tokenResult) {
      console.log('✅ 获取authenticity_token成功')
      console.log(`   Token: ${twitter.getAuthenticityToken()?.substring(0, 20)}...`)
      
      // 继续测试OAuth授权
      console.log('\n2. 测试OAuth授权...')
      twitter.resetTokens() // 重置tokens
      
      const authResult = await twitter.twitterAuthorize(oauthToken.trim())
      
      if (authResult) {
        console.log('✅ OAuth授权成功')
        console.log(`   OAuth Verifier: ${twitter.getOauthVerifier()?.substring(0, 20)}...`)
        
        // 测试回调处理
        console.log('\n3. 测试回调处理...')
        const callbackResult = await twitter.handleOAuthCallback(
          oauthToken.trim(),
          twitter.getOauthVerifier()
        )
        
        if (callbackResult.success) {
          console.log('✅ 回调处理成功')
          console.log(`   状态码: ${callbackResult.status}`)
          console.log(`   最终URL: ${callbackResult.finalUrl || 'N/A'}`)
        } else {
          console.log('❌ 回调处理失败')
          console.log(`   错误: ${callbackResult.error}`)
        }
        
      } else {
        console.log('❌ OAuth授权失败')
      }
      
    } else {
      console.log('❌ 获取authenticity_token失败')
      console.log('\n可能的原因:')
      console.log('1. Twitter auth_token无效或已过期')
      console.log('2. OAuth token无效或已过期')
      console.log('3. 网络连接问题')
      console.log('\n建议:')
      console.log('1. 重新获取Twitter auth_token (从x.com的cookies)')
      console.log('2. 重新获取OAuth token (从Theoriq API)')
    }
    
    console.log('\n=== 测试完成 ===')
    
  } catch (error) {
    console.error('测试过程中发生错误:', error.message)
    console.log('\n调试信息:')
    console.log('- 检查网络连接')
    console.log('- 检查Twitter auth_token是否有效')
    console.log('- 检查OAuth token是否有效')
  }
}

/**
 * 验证Twitter auth_token
 */
async function validateAuthToken() {
  try {
    console.log('=== 验证Twitter Auth Token ===\n')
    
    const fs = require('fs')
    const csvContent = fs.readFileSync('./twitter-bindings-example.csv', 'utf8')
    const lines = csvContent.trim().split('\n')
    const [walletIndex, twitterAuthToken] = lines[1].split(',')
    
    console.log(`Twitter Auth Token: ${twitterAuthToken.substring(0, 10)}...`)
    
    const twitter = new Twitter(twitterAuthToken.trim())
    
    // 尝试访问Twitter API来验证token
    const axios = require('axios')
    
    try {
      const response = await axios.get('https://api.x.com/1.1/account/verify_credentials.json', {
        headers: {
          'authorization': twitter.bearerToken,
          'cookie': `auth_token=${twitterAuthToken.trim()}`,
          'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        },
        timeout: 10000
      })
      
      console.log('✅ Twitter auth_token有效')
      console.log(`   用户信息: ${response.data.screen_name || 'N/A'}`)
      
    } catch (error) {
      console.log('❌ Twitter auth_token可能无效')
      console.log(`   错误: ${error.response?.status || error.message}`)
      console.log('\n请重新获取auth_token:')
      console.log('1. 登录 x.com')
      console.log('2. 开发者工具 -> Application -> Cookies')
      console.log('3. 复制 auth_token 的值')
    }
    
  } catch (error) {
    console.error('验证过程中发生错误:', error.message)
  }
}

/**
 * 生成测试用的OAuth token
 * 注意：这只是一个示例，实际的OAuth token需要从Theoriq API获取
 */
function generateTestOAuthToken() {
  console.log('=== 生成测试OAuth Token ===\n')
  
  // 这是一个示例格式，实际token需要从API获取
  const testToken = 'UyK0UAAAAAABoeyUAAABl26malA'
  
  console.log('⚠️  注意：这只是一个示例OAuth token')
  console.log('实际使用时，您需要:')
  console.log('1. 登录Theoriq平台')
  console.log('2. 调用 /api/social/twitter/request-token API')
  console.log('3. 获取真实的oauth_token')
  console.log('')
  console.log(`示例OAuth Token: ${testToken}`)
  console.log('')
  console.log('如果您有真实的OAuth token，请替换CSV文件中的值')
}

/**
 * 显示使用说明
 */
function showUsage() {
  console.log('=== 简单Twitter测试工具 ===\n')
  
  console.log('命令:')
  console.log('  node simple-twitter-test.js           # 运行完整测试')
  console.log('  node simple-twitter-test.js validate  # 验证auth_token')
  console.log('  node simple-twitter-test.js token     # 生成测试token')
  console.log('  node simple-twitter-test.js help      # 显示帮助\n')
  
  console.log('准备工作:')
  console.log('1. 获取Twitter auth_token:')
  console.log('   - 登录 x.com')
  console.log('   - F12 -> Application -> Cookies -> auth_token')
  console.log('')
  console.log('2. 获取OAuth token:')
  console.log('   - 从Theoriq API获取')
  console.log('   - 或使用测试token进行初步验证')
  console.log('')
  console.log('3. 更新CSV文件:')
  console.log('   - 编辑 twitter-bindings-example.csv')
  console.log('   - 设置正确的auth_token和oauth_token')
}

// 主程序
if (require.main === module) {
  const args = process.argv.slice(2)
  
  if (args[0] === 'help' || args[0] === '-h') {
    showUsage()
  } else if (args[0] === 'validate') {
    validateAuthToken()
  } else if (args[0] === 'token') {
    generateTestOAuthToken()
  } else {
    simpleTest()
  }
}

module.exports = { simpleTest, validateAuthToken }
