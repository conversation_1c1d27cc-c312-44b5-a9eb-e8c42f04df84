const ethers = require('ethers');
const Sosovalue = require('./sosovalue/sosovalue');
const fs = require('fs');
const readline = require('readline');

/**
 * Sosovalue Twitter绑定实际操作脚本
 */
class SosovalueTwitterBinder {
    constructor() {
        this.wallet = null;
        this.sosovalue = null;
        this.token = null;
        this.userId = null;
        this.username = null;
    }

    /**
     * 创建readline接口用于用户输入
     */
    createReadlineInterface() {
        return readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
    }

    /**
     * 获取用户输入
     */
    async getUserInput(question) {
        const rl = this.createReadlineInterface();
        return new Promise((resolve) => {
            rl.question(question, (answer) => {
                rl.close();
                resolve(answer.trim());
            });
        });
    }

    /**
     * 初始化钱包
     */
    async initWallet() {
        try {
            console.log('=== 初始化钱包 ===');
            
            // 从CSV文件读取钱包信息
            const csvContent = fs.readFileSync('./twitter-bindings-example.csv', 'utf8');
            const lines = csvContent.trim().split('\n');
            
            if (lines.length < 2) {
                throw new Error('CSV文件格式错误或数据为空');
            }
            
            // 解析CSV数据
            const [walletIndex, twitterAuthToken] = lines[1].split(',');
            
            // 从wallets.csv读取钱包私钥
            const walletsCsvContent = fs.readFileSync('./wallets.csv', 'utf8');
            const walletLines = walletsCsvContent.trim().split('\n');
            
            let privateKey = null;
            for (let i = 1; i < walletLines.length; i++) {
                const [index, addr, pk] = walletLines[i].split(',');
                if (index === walletIndex) {
                    privateKey = pk.trim();
                    break;
                }
            }
            
            if (!privateKey) {
                throw new Error(`未找到钱包索引 ${walletIndex} 的私钥`);
            }
            
            // 创建钱包实例
            this.wallet = new ethers.Wallet(privateKey);
            this.sosovalue = new Sosovalue(this.wallet);
            
            console.log(`✅ 钱包初始化成功`);
            console.log(`   地址: ${this.wallet.address}`);
            
            return { success: true };
            
        } catch (error) {
            console.error(`❌ 钱包初始化失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 完整的登录流程
     */
    async performLogin() {
        try {
            console.log('\n=== 用户登录流程 ===');
            
            // 获取CF Token
            console.log('正在获取CF Token...');
            const cfToken = await this.sosovalue.getLocalCaptcha();
            console.log('✅ CF Token获取成功');
            
            // 用户登录
            console.log('正在进行用户登录...');
            const loginResult = await this.sosovalue.login(null, cfToken);
            
            if (loginResult.success) {
                this.token = loginResult.token;
                this.userId = loginResult.userId;
                this.username = loginResult.username;
                
                console.log('✅ 用户登录成功');
                console.log(`   User ID: ${this.userId}`);
                console.log(`   Username: ${this.username}`);
                console.log(`   过期时间: ${loginResult.expiration}`);
                
                return { success: true };
            } else {
                throw new Error(loginResult.msg);
            }
            
        } catch (error) {
            console.error(`❌ 登录失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 获取Twitter授权URL并指导用户操作
     */
    async getTwitterAuthAndGuide() {
        try {
            console.log('\n=== 获取Twitter授权URL ===');
            
            const authUrlResult = await this.sosovalue.getTwitterAuthUrl(this.token);
            
            if (authUrlResult.success) {
                console.log('✅ Twitter授权URL获取成功');
                
                // 从URL中提取oauth_token
                const url = new URL(authUrlResult.authUrl);
                const oauthToken = url.searchParams.get('oauth_token');
                
                console.log('\n' + '='.repeat(60));
                console.log('🔗 请完成Twitter授权操作');
                console.log('='.repeat(60));
                console.log('');
                console.log('1. 复制以下URL并在浏览器中打开:');
                console.log(`   ${authUrlResult.authUrl}`);
                console.log('');
                console.log('2. 使用您的Twitter账号登录');
                console.log('');
                console.log('3. 点击"授权应用"按钮');
                console.log('');
                console.log('4. 授权完成后，您将被重定向到回调URL');
                console.log('   回调URL格式类似:');
                console.log('   https://sosovalue.com?oauth_token=XXX&oauth_verifier=YYY');
                console.log('');
                console.log('5. 从回调URL中复制 oauth_verifier 参数的值');
                console.log('');
                console.log('='.repeat(60));
                
                return { success: true, oauthToken };
            } else {
                throw new Error(authUrlResult.msg);
            }
            
        } catch (error) {
            console.error(`❌ 获取Twitter授权URL失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 执行Twitter绑定
     */
    async performTwitterBinding(oauthToken) {
        try {
            console.log('\n=== Twitter账号绑定 ===');
            
            // 获取用户输入的oauth_verifier
            const oauthVerifier = await this.getUserInput('请输入从回调URL中获取的 oauth_verifier: ');
            
            if (!oauthVerifier) {
                throw new Error('oauth_verifier不能为空');
            }
            
            console.log('\n正在绑定Twitter账号...');
            console.log(`OAuth Token: ${oauthToken}`);
            console.log(`OAuth Verifier: ${oauthVerifier.substring(0, 20)}...`);
            
            const bindResult = await this.sosovalue.bindTwitter(this.token, oauthToken, oauthVerifier);
            
            if (bindResult.success) {
                console.log('\n🎉 Twitter绑定成功!');
                console.log(`✅ ${bindResult.msg}`);
                return { success: true };
            } else {
                throw new Error(bindResult.msg);
            }
            
        } catch (error) {
            console.error(`❌ Twitter绑定失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 运行完整的绑定流程
     */
    async runBinding() {
        console.log('🚀 开始Sosovalue Twitter账号绑定\n');
        
        try {
            // 初始化钱包
            const walletResult = await this.initWallet();
            if (!walletResult.success) {
                throw new Error('钱包初始化失败');
            }
            
            // 用户登录
            const loginResult = await this.performLogin();
            if (!loginResult.success) {
                throw new Error('用户登录失败');
            }
            
            // 获取Twitter授权URL
            const authResult = await this.getTwitterAuthAndGuide();
            if (!authResult.success) {
                throw new Error('获取Twitter授权URL失败');
            }
            
            // 等待用户完成授权
            console.log('\n⏳ 请完成上述Twitter授权步骤...');
            const continueBinding = await this.getUserInput('完成授权后，按回车键继续绑定流程 (或输入 "q" 退出): ');
            
            if (continueBinding.toLowerCase() === 'q') {
                console.log('👋 用户取消绑定流程');
                return;
            }
            
            // 执行Twitter绑定
            const bindResult = await this.performTwitterBinding(authResult.oauthToken);
            
            if (bindResult.success) {
                console.log('\n' + '='.repeat(50));
                console.log('🎉 Twitter账号绑定完成!');
                console.log('='.repeat(50));
                console.log('✅ 所有步骤都已成功完成');
                console.log('✅ 您的Twitter账号已成功绑定到Sosovalue');
            } else {
                throw new Error('Twitter绑定失败');
            }
            
        } catch (error) {
            console.log('\n' + '='.repeat(50));
            console.log('❌ 绑定流程失败!');
            console.log('='.repeat(50));
            console.error(`错误: ${error.message}`);
            console.log('\n💡 请检查:');
            console.log('   - 网络连接是否正常');
            console.log('   - Twitter授权是否完成');
            console.log('   - oauth_verifier是否正确');
        }
    }

    /**
     * 显示使用说明
     */
    static showUsage() {
        console.log('=== Sosovalue Twitter绑定工具 ===\n');
        console.log('功能: 自动化完成Sosovalue平台的Twitter账号绑定');
        console.log('');
        console.log('使用方法:');
        console.log('  node sosovalue-twitter-bind.js        # 开始绑定流程');
        console.log('  node sosovalue-twitter-bind.js help   # 显示帮助');
        console.log('');
        console.log('准备工作:');
        console.log('  1. 确保 wallets.csv 文件包含钱包信息');
        console.log('  2. 确保 twitter-bindings-example.csv 文件包含Twitter auth token');
        console.log('  3. 确保验证码API服务运行在 http://192.168.0.138:3000');
        console.log('  4. 准备好您的Twitter账号用于授权');
        console.log('');
        console.log('注意事项:');
        console.log('  - 整个流程需要手动完成Twitter授权步骤');
        console.log('  - 请确保网络连接稳定');
        console.log('  - oauth_verifier需要从回调URL中手动获取');
    }
}

// 主程序
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args[0] === 'help' || args[0] === '-h') {
        SosovalueTwitterBinder.showUsage();
    } else {
        const binder = new SosovalueTwitterBinder();
        binder.runBinding();
    }
}

module.exports = SosovalueTwitterBinder;
