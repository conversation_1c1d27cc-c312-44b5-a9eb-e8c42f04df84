const ethers = require("ethers");
const axios = require('axios');
const fakeua = require('fake-useragent');

class Sosovalue {
    constructor(wallet) {
        this.wallet = wallet;
        this.websiteUrl = 'https://app.sosovalue.com/';
        this.websiteKey = '0x4AAAAAAA4PZrjDa5PcluqN';
    }

    /**
     * 替代 requestWithProxy 的方法，使用原生 axios
     * @param {string} url - 请求URL
     * @param {string} proxyString - 代理字符串（暂时忽略）
     * @param {string} method - HTTP方法
     * @param {object} data - 请求数据
     * @param {object} headers - 请求头
     * @returns {Promise<object>} 响应对象
     */
    async makeRequest(url, proxyString, method, data, headers) {
        try {
            const config = {
                method: method.toLowerCase(),
                url: url,
                headers: headers,
                timeout: 30000,
                validateStatus: function (status) {
                    return status >= 200 && status < 500; // 接受2xx-4xx状态码
                }
            };

            // 根据方法添加数据
            if (method.toLowerCase() === 'post' || method.toLowerCase() === 'put') {
                config.data = data;
            } else if (method.toLowerCase() === 'get' && data) {
                config.params = data;
            }

            const response = await axios(config);

            // 返回与原 requestWithProxy 相同的格式
            return {
                data: response.data,
                status: response.status,
                headers: response.headers
            };
        } catch (error) {
            // 如果是HTTP错误响应，仍然返回响应数据
            if (error.response) {
                return {
                    data: error.response.data,
                    status: error.response.status,
                    headers: error.response.headers
                };
            }
            throw error;
        }
    }

    async getLocalCaptcha() {
        try {
            const response = await fetch('http://192.168.0.138:3000', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    type: "cftoken",
                    websiteUrl: this.websiteUrl,
                    websiteKey: this.websiteKey
                })
            });

            const result = await response.json();
            console.log('验证码API响应码:', result.code);
            
            if (!result.token) {
                console.error('API响应结构:', result);
                throw new Error('API响应中没有token');
            }

            console.log('Turnstile 令牌获取成功');
            return result.token;
        } catch (error) {
            throw new Error('获取验证码失败: ' + error.message);
        }
    }

    async getNonce(inviteCode) {
        const url = 'https://gw.sosovalue.com/authentication/auth/getChallenge';
        const headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-encoding': 'gzip, deflate, br, zstd',
            'cache-control': 'no-cache',
            'content-type': 'application/json;charset=UTF-8',
            'origin': 'https://app.sosovalue.com',
            'referer': 'https://app.sosovalue.com/',
            'user-agent': fakeua(),
        }
        const data = {
            invitationCode: inviteCode || null,
            invitationOrigin: null,
            walletAddress: this.wallet.address,
        }
        const response = await this.makeRequest(url, null, 'post', data, headers);
        if (response.data.code === 0) {
            return{
                success: true,
                msg: '获取nonce成功',
                data: response.data.data,
            }
        } else {
            return{
                success: false,
                msg: response.data.message,
            }
        }
    }

    async login(inviteCode, captchaToken) {
        const nonce = await this.getNonce(inviteCode);
        if (!nonce.success) {
            return{
                success: false,
                msg: nonce.msg,
            }
        }
        const nonceData = nonce.data.hmac;
        const inviterWalletAddress = nonce.data.inviterWalletAddress;

        const url = `https://gw.sosovalue.com/authentication/auth/thirdPartyWalletLoginV2?cf-turnstile-response=${captchaToken}`;
        const headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-encoding': 'gzip, deflate, br, zstd',
            'cache-control': 'no-cache',
            'content-type': 'application/json;charset=UTF-8',
            'origin': 'https://app.sosovalue.com',
            'referer': 'https://app.sosovalue.com/',
            'user-agent': fakeua(),
        }
        const invmsg = `\n      By connecting your wallet to the Website, you acknowledge and agree to be bound by the Terms of Use ([https://sosovalue.gitbook.io/helpdocument/resources/terms-of-use]) and Privacy Policy ([https://sosovalue.gitbook.io/helpdocument/resources/privacy-policy]), including all disclaimers and limitations of liability. Access is restricted for individuals in Excluded Jurisdictions or who qualify as Excluded Persons under the Terms. Your continued use following updates constitutes acceptance of the revised Terms.  By proceeding, you represent that you have the authority to bind yourself and any represented entity to these Terms.\n\n\n       \n          I confirm that ${inviterWalletAddress} invited me to register at sosovalue.com\n      \n         Nonce: ${nonceData}`
        const msg = `\n      By connecting your wallet to the Website, you acknowledge and agree to be bound by the Terms of Use ([https://sosovalue.gitbook.io/helpdocument/resources/terms-of-use]) and Privacy Policy ([https://sosovalue.gitbook.io/helpdocument/resources/privacy-policy]), including all disclaimers and limitations of liability. Access is restricted for individuals in Excluded Jurisdictions or who qualify as Excluded Persons under the Terms. Your continued use following updates constitutes acceptance of the revised Terms.  By proceeding, you represent that you have the authority to bind yourself and any represented entity to these Terms.\n\n\n        Nonce: ${nonceData}`
        
        // 根据是否有邀请码决定签名哪个消息
        const messageToSign = (inviteCode && inviteCode !== null) ? invmsg : msg;
        const signatureHex = await this.wallet.signMessage(messageToSign);

        const data = {
            invitationCode: inviteCode || null,
            invitationFrom: null,
            invitationOrigin: null,
            message: msg,
            signatureHex: signatureHex,
            thirdpartyId: this.wallet.address,
            thirdpartyName: "rainbowkit",
            walletName: "metamask",
        }
        const response = await this.makeRequest(url, null, 'post', data, headers);
        if (response.data.code === 0) {
            return{
                success: true,
                msg: '登录成功',
                data: response.data.data,
                token: response.data.data.token,
                userId: response.data.data.userId,
                expire: response.data.data.expire,
                expiration: response.data.data.expiration,
                expirationTs: response.data.data.expirationTs,
                username: response.data.data.username,
            }
        } else {
            return{
                success: false,
                msg: response.data.message,
            }
        }
    }

    async createUser(token, userId, username) {
        const url = 'https://gw.sosovalue.com/data/s-user-action-log-do/create';
        const headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-encoding': 'gzip, deflate, br, zstd',
            'cache-control': 'no-cache',
            'authorization': `Bearer ${token}`,
            'content-type': 'application/json;charset=UTF-8',
            'origin': 'https://app.sosovalue.com',
            'referer': 'https://app.sosovalue.com/',
            'user-agent':'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        }
        const data = {
            actionClassify: 1,
            actionId: 1,
            createTime: Math.floor(new Date().getTime() / 1000) * 1000, // 转换为秒级时间戳然后乘以1000
            module: 1,
            spendTime: Math.floor(Math.random() * (3000 - 800) + 800), // 随机生成800-3000毫秒的停留时间
            userBrowser: "Chrome",
            userDevice: "Mac OS",
            userId: userId,
            username: username,
        }
        console.log(JSON.stringify(data));
        const response = await this.makeRequest(url, null, 'post', data, headers);
        if (response.data.code === 0) {
            return{
                success: true,
                msg: '创建用户成功',
            }
        } else {
            return{
                success: false,
                msg: response.data.message,
            }
        }
    }


    async checkIn(token, userId, username) {
        try {
            // // 第一步：创建用户行为日志
            // const createUser = await this.createUser(token, userId, username);
            // if (!createUser.success) {
            //     return{
            //         success: false,
            //         msg: createUser.msg,
            //     }
            // }

            console.log('token:', token);

            // 第一步：获取签到状态
            const checkInUrl = 'https://gw.sosovalue.com/rights/daily-checkin-do/findPage';
            const checkInHeaders = {
                'accept': 'application/json, text/plain, */*',
                'accept-encoding': 'gzip, deflate, br, zstd',
                'cache-control': 'no-cache',
                'authorization': `Bearer ${token}`,
                'content-type': 'application/json;charset=UTF-8',
                'origin': 'https://app.sosovalue.com',
                'referer': 'https://app.sosovalue.com/',
                'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            }

            const checkInData = {
                pageSize: 1,
                userId: userId,
            }

            console.log('开始获取签到状态...');
            const checkInResponse = await this.makeRequest(checkInUrl, null, 'post', checkInData, checkInHeaders);
            console.log('签到状态响应:', JSON.stringify(checkInResponse.data));

            // 第二步：更新登录状态（带重试机制）
            let updateStatusSuccess = false;
            let retryCount = 0;
            const maxRetries = 1;
            
            while (!updateStatusSuccess && retryCount < maxRetries) {
                try {
                    if (retryCount > 0) {
                        console.log(`第${retryCount + 1}次重试更新登录状态...`);
                        // 重试前等待随机时间，避免并发冲突
                        await new Promise(resolve => setTimeout(resolve, Math.random() * 2000 + 1000));
                    }
                    
                    const updateStatusUrl = 'https://gw.sosovalue.com/usercenter/user/updateLoginStatus';
                    const updateStatusHeaders = {
                        'accept': 'application/json, text/plain, */*',
                        'accept-encoding': 'gzip, deflate, br, zstd',
                        'cache-control': 'no-cache',
                        'authorization': `Bearer ${token}`,
                        'content-type': 'application/json;charset=UTF-8',
                        'origin': 'https://app.sosovalue.com',
                        'referer': 'https://app.sosovalue.com/',
                        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    }
                    
                    console.log('开始请求更新登录状态...');
                    
                    const updateStatusResponse = await this.makeRequest(updateStatusUrl, null, 'put', null, updateStatusHeaders);
                    console.log('updateStatusResponse:', JSON.stringify(updateStatusResponse));
                    
                    if (updateStatusResponse && updateStatusResponse.data) {
                        console.log('更新登录状态响应:', JSON.stringify(updateStatusResponse.data));
                        
                        if (updateStatusResponse.data.code === 0) {
                            updateStatusSuccess = true;
                            console.log('更新登录状态成功');
                        } else if (updateStatusResponse.data.msg && updateStatusResponse.data.msg.includes('Sensitive Operation by Others')) {
                            console.log(`检测到敏感操作冲突，第${retryCount + 1}次重试...`);
                            retryCount++;
                            if (retryCount >= maxRetries) {
                                return{
                                    success: false,
                                    msg: '更新登录状态失败: 检测到其他敏感操作，请稍后重试',
                                }
                            }
                        } else {
                            return{
                                success: false,
                                msg: '更新登录状态失败: ' + updateStatusResponse.data.msg,
                            }
                        }
                    } else {
                        console.log('更新登录状态响应异常:', updateStatusResponse);
                        retryCount++;
                        if (retryCount >= maxRetries) {
                            return{
                                success: false,
                                msg: '更新登录状态响应异常',
                            }
                        }
                    }
                } catch (updateError) {
                    console.error('更新登录状态请求失败:', updateError);
                    retryCount++;
                    if (retryCount >= maxRetries) {
                        return{
                            success: false,
                            msg: '更新登录状态请求失败: ' + updateError.message,
                        }
                    }
                }
            }

            // 第三步：处理签到结果
            if (checkInResponse.data.code === 0) {
                // 检查list数组是否存在且不为空
                const list = checkInResponse.data.data?.list;
                if (list && list.length > 0) {
                    const consecutiveDays = list[0].consecutiveDays;
                    const status = list[0].status; // 1表示已签到
                    const rewardStatus = list[0].rewardStatus; // 1表示已领取奖励
                    
                    return{
                        success: true,
                        msg: `签到成功,连续签到天数：${consecutiveDays}天`,
                        data: {
                            consecutiveDays: consecutiveDays,
                            status: status,
                            rewardStatus: rewardStatus,
                            checkinDate: list[0].checkinDate
                        }
                    }
                } else {
                    // 如果list为空，可能是首次签到
                    return{
                        success: true,
                        msg: '签到成功,这是您的第一次签到',
                        data: {
                            consecutiveDays: 1,
                            status: 1,
                            rewardStatus: 1,
                            checkinDate: Math.floor(new Date().getTime() / 1000) * 1000
                        }
                    }
                }
            } else {
                return{
                    success: false,
                    msg: checkInResponse.data.message,
                }
            }
        } catch (error) {
            return{
                success: false,
                msg: '签到过程中发生错误: ' + error.message,
            }
        }
    }

    async getTwitterAuthUrl(token) {
        try {
            const url = 'https://sosovalue.com/api/twitter-sign?redirectUri=https://sosovalue.com';
            const headers = {
                'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'accept-encoding': 'gzip, deflate, br, zstd',
                'accept-language': 'zh-CN,zh;q=0.9',
                'content-type': 'application/json',
                'referer': 'https://sosovalue.com/zh/center',
                'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }
            
            // 使用axios直接请求以处理重定向
            const axiosConfig = {
                method: 'get',
                url: url,
                headers: headers,
                maxRedirects: 0, // 禁止自动重定向
                validateStatus: function (status) {
                    return status >= 200 && status < 400; // 接受2xx和3xx状态码
                }
            };

            // 如果有代理，配置代理
            if (this.proxyString) {
                const proxyArray = this.proxyString.split(':');
                axiosConfig.proxy = {
                    host: proxyArray[0],
                    port: proxyArray[1],
                    auth: proxyArray[2] && proxyArray[3] ? {
                        username: proxyArray[2],
                        password: proxyArray[3]
                    } : undefined
                };
            }

            const response = await axios(axiosConfig);
            
            // 检查是否是重定向响应
            if (response.status === 307 && response.headers.location) {
                return {
                    success: true,
                    msg: '获取Twitter授权URL成功',
                    authUrl: response.headers.location
                };
            } else {
                throw new Error('未获取到重定向URL');
            }
        } catch (error) {
            if (error.response && error.response.status === 307 && error.response.headers.location) {
                return {
                    success: true,
                    msg: '获取Twitter授权URL成功',
                    authUrl: error.response.headers.location
                };
            } else {
                throw new Error(error.message);
            }
        }
    }

    async bindTwitter(token, oauthToken, oauthVerifier) {
        try {
            const url = 'https://gw.sosovalue.com/usercenter/user/bindTwitter';
            const headers = {
                'accept': 'application/json, text/plain, */*',
                'accept-encoding': 'gzip, deflate, br, zstd',
                'cache-control': 'no-cache',
                'authorization': `Bearer ${token}`,
                'content-type': 'application/json;charset=UTF-8',
                'origin': 'https://sosovalue.com',
                'referer': 'https://sosovalue.com/zh/center',
                'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }

            const data = {
                oauthToken: oauthToken,
                oauthVerifier: oauthVerifier,
                thirdpartyName: 'twitter',
            }

            const response = await this.makeRequest(url, null, 'post', data, headers);
            if (response.data.code === 0) {
                return{
                    success: true,
                    msg: '绑定Twitter成功',
                }
            } else {
                return{
                    success: false,
                    msg: response.data.message,
                }
            }
        } catch (error) {
            return{
                success: false,
                msg: error.message,
            }
        }
    }
}

module.exports = Sosovalue;
