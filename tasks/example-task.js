const theoriqManager = require('../TheoriqManager')
const logger = require('../utils/logger')

/**
 * 示例任务：获取用户信息
 * 这是一个示例，展示如何创建自己的任务
 */
async function exampleTask(walletIndex) {
  try {
    logger.info(`开始执行示例任务 - 钱包 ${walletIndex}`)
    
    // 1. 确保钱包已登录
    const token = theoriqManager.getWalletToken(walletIndex)
    if (!token) {
      throw new Error('钱包未登录')
    }
    
    // 2. 发送API请求 - 这里只是一个示例
    // 实际使用时，替换为真正的Theoriq API端点
    const result = await theoriqManager.sendAuthRequest(
      walletIndex,
      'GET',
      'https://api-theoriq.bonusblock.io/api/user/profile'
    )
    
    logger.success(`任务完成 - 钱包 ${walletIndex}`, result)
    
    return { success: true, data: result }
    
  } catch (error) {
    logger.error(`任务失败 - 钱包 ${walletIndex}`, error.message)
    return { success: false, error: error.message }
  }
}

/**
 * 批量执行任务
 */
async function runBatchTask(walletIndexes = []) {
  const allWallets = theoriqManager.getStats().wallets
  const targetWallets = walletIndexes.length > 0 
    ? allWallets.filter(w => walletIndexes.includes(w.walletIndex))
    : allWallets.filter(w => w.isLoggedIn) // 只处理已登录的钱包
    
  logger.taskStart(`批量执行示例任务 - ${targetWallets.length} 个钱包`)
  
  const results = []
  let success = 0, failed = 0
  
  for (const wallet of targetWallets) {
    const result = await exampleTask(wallet.walletIndex)
    results.push({ walletIndex: wallet.walletIndex, ...result })
    
    if (result.success) {
      success++
    } else {
      failed++
    }
    
    // 随机延迟1-3秒
    if (wallet.walletIndex !== targetWallets[targetWallets.length - 1].walletIndex) {
      const delay = 1000 + Math.random() * 2000
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
  
  logger.taskEnd('批量任务', { total: targetWallets.length, success, failed })
  
  return { total: targetWallets.length, success, failed, results }
}

// 如果直接运行此文件
if (require.main === module) {
  (async () => {
    try {
      // 执行所有已登录钱包的任务
      await runBatchTask()
    } catch (error) {
      console.error('任务执行失败:', error.message)
      process.exit(1)
    }
  })()
}

module.exports = {
  exampleTask,
  runBatchTask
} 