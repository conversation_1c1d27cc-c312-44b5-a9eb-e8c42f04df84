const theoriqManager = require('../TheoriqManager')
const Twitter = require('../x/twitter')
const logger = require('../utils/logger')

/**
 * Twitter绑定任务
 * @param {number} walletIndex 钱包索引
 * @param {string} twitterAuthToken Twitter的auth_token
 * @param {string} oauthToken 从Theoriq获取的oauth_token
 * @param {boolean} handleCallback 是否处理回调（默认true）
 * @returns {Object} 任务结果
 */
async function bindTwitter(walletIndex, twitterAuthToken, oauthToken, handleCallback = true) {
  try {
    logger.info(`开始Twitter绑定任务 - 钱包 ${walletIndex}`)
    
    // 1. 确保钱包已登录Theoriq（如果需要后续API调用）
    const token = theoriqManager.getWalletToken(walletIndex)
    if (!token) {
      logger.warn(`钱包 ${walletIndex} 未登录Theoriq，但可以继续Twitter绑定`)
    }
    
    // 2. 获取代理配置
    const proxyUrl = theoriqManager.config.proxy?.enabled ? theoriqManager.config.proxy.url : null
    
    // 3. 创建Twitter实例
    const twitter = new Twitter(twitterAuthToken, proxyUrl)
    
    // 4. 执行完整的Twitter OAuth流程（包含回调处理）
    const oauthResult = await twitter.completeOAuthFlow(oauthToken, handleCallback)
    
    if (!oauthResult.success) {
      throw new Error(`Twitter OAuth失败: ${oauthResult.error}`)
    }
    
    logger.success(`Twitter OAuth成功 - 钱包 ${walletIndex}`, {
      verifier: oauthResult.oauthVerifier?.substring(0, 10) + '...',
      callbackHandled: handleCallback,
      callbackSuccess: oauthResult.callback?.success || false
    })
    
    return {
      success: true,
      data: {
        oauthToken: oauthToken,
        oauthVerifier: oauthResult.oauthVerifier,
        authenticityToken: oauthResult.authenticityToken,
        callback: oauthResult.callback
      }
    }
    
  } catch (error) {
    logger.error(`Twitter绑定失败 - 钱包 ${walletIndex}`, error.message)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 仅执行Twitter OAuth，不处理回调
 * @param {number} walletIndex 钱包索引
 * @param {string} twitterAuthToken Twitter的auth_token
 * @param {string} oauthToken 从Theoriq获取的oauth_token
 * @returns {Object} 任务结果
 */
async function twitterOAuthOnly(walletIndex, twitterAuthToken, oauthToken) {
  return await bindTwitter(walletIndex, twitterAuthToken, oauthToken, false)
}

/**
 * 批量Twitter绑定任务
 * @param {Array} bindings 绑定配置数组 [{walletIndex, twitterAuthToken, oauthToken}]
 * @param {boolean} handleCallback 是否处理回调（默认true）
 * @returns {Object} 批量任务结果
 */
async function batchBindTwitter(bindings, handleCallback = true) {
  logger.taskStart(`批量Twitter绑定 - ${bindings.length} 个任务`)
  
  const results = []
  let success = 0, failed = 0
  
  for (const binding of bindings) {
    const { walletIndex, twitterAuthToken, oauthToken } = binding
    
    const result = await bindTwitter(walletIndex, twitterAuthToken, oauthToken, handleCallback)
    results.push({ walletIndex, ...result })
    
    if (result.success) {
      success++
    } else {
      failed++
    }
    
    // 随机延迟2-5秒
    if (binding !== bindings[bindings.length - 1]) {
      const delay = 2000 + Math.random() * 3000
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
  
  logger.taskEnd('批量Twitter绑定', { total: bindings.length, success, failed })
  
  return { total: bindings.length, success, failed, results }
}

/**
 * 从CSV文件读取Twitter绑定配置
 * CSV格式: walletIndex,twitterAuthToken,oauthToken
 * @param {string} csvPath CSV文件路径
 * @returns {Array} 绑定配置数组
 */
function loadTwitterBindingsFromCSV(csvPath) {
  try {
    const fs = require('fs')
    const content = fs.readFileSync(csvPath, 'utf-8')
    const lines = content.split('\n').filter(line => line.trim())
    
    // 跳过表头
    const dataLines = lines.slice(1)
    
    return dataLines.map(line => {
      const [walletIndex, twitterAuthToken, oauthToken] = line.split(',').map(item => item.trim())
      return {
        walletIndex: parseInt(walletIndex),
        twitterAuthToken,
        oauthToken
      }
    })
  } catch (error) {
    logger.error('读取Twitter绑定CSV失败', error.message)
    return []
  }
}

/**
 * 使用示例
 */
async function example() {
  try {
    console.log('=== Twitter绑定任务示例 ===\n')
    
    // 单个绑定示例
    const result = await bindTwitter(
      1, // 钱包索引
      'your_twitter_auth_token_here', // Twitter的auth_token
      'your_oauth_token_from_theoriq' // 从Theoriq获取的oauth_token
    )
    
    console.log('绑定结果:', result)
    
    // 批量绑定示例
    const bindings = [
      {
        walletIndex: 1,
        twitterAuthToken: 'twitter_auth_token_1',
        oauthToken: 'oauth_token_1'
      },
      {
        walletIndex: 2,
        twitterAuthToken: 'twitter_auth_token_2',
        oauthToken: 'oauth_token_2'
      }
    ]
    
    const batchResult = await batchBindTwitter(bindings)
    console.log('批量绑定结果:', batchResult)
    
    // 从CSV文件批量绑定示例
    // const csvBindings = loadTwitterBindingsFromCSV('./twitter-bindings.csv')
    // const csvResult = await batchBindTwitter(csvBindings)
    // console.log('CSV批量绑定结果:', csvResult)
    
  } catch (error) {
    console.error('示例执行失败:', error.message)
  }
}

// 如果直接运行此文件
if (require.main === module) {
  const args = process.argv.slice(2)
  
  if (args[0] === 'help' || args[0] === '-h') {
    console.log('=== Twitter绑定任务使用说明 ===\n')
    console.log('使用方法:')
    console.log('  node tasks/twitter-bind.js                    - 运行示例')
    console.log('  node tasks/twitter-bind.js help               - 显示帮助\n')
    console.log('主要函数:')
    console.log('  bindTwitter(walletIndex, authToken, oauthToken)')
    console.log('  twitterOAuthOnly(walletIndex, authToken, oauthToken)')
    console.log('  batchBindTwitter(bindings)')
    console.log('  loadTwitterBindingsFromCSV(csvPath)')
  } else {
    example()
  }
}

module.exports = {
  bindTwitter,
  twitterOAuthOnly,
  batchBindTwitter,
  loadTwitterBindingsFromCSV,
  Twitter
} 