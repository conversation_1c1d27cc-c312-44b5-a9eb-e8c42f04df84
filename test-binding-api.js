const SosovalueAPI = require('./sosovalue-api');
const WalletManager = require('./wallet-manager');

/**
 * 测试Sosovalue绑定API
 */
class BindingAPITester {
    constructor() {
        this.sosovalue = new SosovalueAPI();
        this.walletManager = new WalletManager();
        this.userToken = null;
    }

    /**
     * 初始化并获取用户token
     */
    async initializeUser() {
        try {
            console.log('=== 初始化用户登录 ===');
            
            // 初始化钱包
            const wallet = this.walletManager.getWallet(1);
            console.log(`钱包地址: ${wallet.address}`);
            
            // 获取CF Token
            console.log('正在获取CF Token...');
            const cfToken = await this.sosovalue.getCFToken();
            if (!cfToken) {
                throw new Error('获取CF Token失败');
            }
            console.log('✅ CF Token获取成功');
            
            // 用户登录
            console.log('正在进行用户登录...');
            const loginResult = await this.sosovalue.login(wallet.address, wallet.privateKey, cfToken);
            
            if (!loginResult.success) {
                throw new Error(`用户登录失败: ${loginResult.msg}`);
            }
            
            this.userToken = loginResult.token;
            console.log('✅ 用户登录成功');
            console.log(`   User ID: ${loginResult.userId}`);
            console.log(`   Username: ${loginResult.username}`);
            console.log(`   Token: ${this.userToken.substring(0, 20)}...`);
            
            return true;
            
        } catch (error) {
            console.error(`❌ 初始化失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 测试Twitter绑定API
     */
    async testTwitterBinding() {
        try {
            console.log('\n=== 测试Twitter绑定API ===');
            
            // 使用我们已经获取到的有效参数
            const oauthToken = 'MsnLbAAAAAABpaBwAAABmCHwvsk';
            const oauthVerifier = 'ZYbHnNCFWzyI6oqPDj10cg4pX5rrWP3f';
            
            console.log(`OAuth Token: ${oauthToken}`);
            console.log(`OAuth Verifier: ${oauthVerifier}`);
            console.log(`User Token: ${this.userToken.substring(0, 20)}...`);
            
            console.log('\n正在调用绑定API...');
            console.log('目标URL: https://gw.sosovalue.com/usercenter/user/bindTwitter');
            
            // 调用绑定API
            const bindResult = await this.sosovalue.bindTwitter(
                this.userToken,
                oauthToken,
                oauthVerifier
            );
            
            console.log('\n=== 绑定API响应 ===');
            console.log(`状态码: ${bindResult.status || '未知'}`);
            console.log(`成功状态: ${bindResult.success}`);
            console.log(`响应消息: ${bindResult.msg}`);
            
            if (bindResult.success) {
                console.log('\n🎉🎉🎉 Twitter账号绑定成功! 🎉🎉🎉');
                console.log('✅ API返回200状态码');
                console.log('✅ 绑定操作完成');
                
                console.log('\n' + '='.repeat(60));
                console.log('🏆 完整流程验证成功!');
                console.log('='.repeat(60));
                console.log('✅ 钱包初始化');
                console.log('✅ 用户登录');
                console.log('✅ Twitter OAuth授权 (手动)');
                console.log('✅ 获取oauth_verifier');
                console.log('✅ Twitter账号绑定');
                console.log('\n🎯 所有步骤都成功完成，Twitter账号已绑定到Sosovalue!');
                
                return { success: true, result: bindResult };
                
            } else {
                console.log('\n❌ Twitter绑定失败');
                console.log(`   错误信息: ${bindResult.msg}`);
                console.log(`   状态码: ${bindResult.status || '未知'}`);
                
                // 分析失败原因
                console.log('\n🔍 失败原因分析:');
                if (bindResult.msg && bindResult.msg.includes('token')) {
                    console.log('   - 可能是OAuth token或verifier无效');
                    console.log('   - 或者用户token已过期');
                } else if (bindResult.msg && bindResult.msg.includes('已绑定')) {
                    console.log('   - 该Twitter账号可能已经绑定到其他用户');
                } else if (bindResult.status === 401) {
                    console.log('   - 用户认证失败，token可能无效');
                } else if (bindResult.status === 400) {
                    console.log('   - 请求参数错误');
                } else {
                    console.log(`   - 其他错误: ${bindResult.msg}`);
                }
                
                return { success: false, result: bindResult };
            }
            
        } catch (error) {
            console.log('\n❌ 测试过程中出错');
            console.error(`   错误: ${error.message}`);
            
            if (error.response) {
                console.log(`   HTTP状态码: ${error.response.status}`);
                console.log(`   响应数据: ${JSON.stringify(error.response.data, null, 2)}`);
            }
            
            return { success: false, error: error.message };
        }
    }

    /**
     * 运行完整测试
     */
    async runCompleteTest() {
        console.log('🚀 开始测试Sosovalue Twitter绑定API\n');
        
        try {
            // 初始化用户
            const initSuccess = await this.initializeUser();
            if (!initSuccess) {
                console.log('❌ 初始化失败，无法继续测试');
                return;
            }
            
            // 等待一下
            await this.delay(2000);
            
            // 测试绑定API
            const testResult = await this.testTwitterBinding();
            
            console.log('\n' + '='.repeat(60));
            console.log('📊 测试结果总结');
            console.log('='.repeat(60));
            
            if (testResult.success) {
                console.log('🎉 测试成功!');
                console.log('✅ Twitter账号绑定API工作正常');
                console.log('✅ 返回200状态码');
                console.log('✅ 完整流程验证通过');
                
                console.log('\n🎯 结论:');
                console.log('我们的Twitter OAuth集成完全正确!');
                console.log('手动授权 + API绑定的完整流程已验证成功。');
                
            } else {
                console.log('⚠️  测试部分成功');
                console.log('✅ 用户登录正常');
                console.log('✅ OAuth参数获取正常');
                console.log('❌ 绑定API调用失败');
                
                console.log('\n🔍 需要进一步调查:');
                console.log('- 检查OAuth token和verifier的有效性');
                console.log('- 检查API参数格式');
                console.log('- 检查用户权限');
            }
            
        } catch (error) {
            console.log('\n❌ 测试失败');
            console.error(`错误: ${error.message}`);
        }
    }

    /**
     * 延迟函数
     */
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 主程序
if (require.main === module) {
    const tester = new BindingAPITester();
    tester.runCompleteTest();
}

module.exports = BindingAPITester;
