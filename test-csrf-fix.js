const axios = require('axios')
const logger = require('./utils/logger')

/**
 * 测试修复CSRF问题的OAuth授权
 */
async function testCSRFFix() {
  try {
    console.log('=== 测试CSRF修复的OAuth授权 ===\n')
    
    // 从CSV读取配置
    const fs = require('fs')
    const csvContent = fs.readFileSync('./twitter-bindings-example.csv', 'utf8')
    const lines = csvContent.trim().split('\n')
    const [walletIndex, twitterAuthToken, oauthToken] = lines[1].split(',')
    
    console.log(`Twitter Auth Token: ${twitterAuthToken.substring(0, 10)}...`)
    console.log(`OAuth Token: ${oauthToken}`)
    
    // 第一步：获取授权页面和CSRF token
    console.log('\n1. 获取授权页面和CSRF token...')
    
    const authUrl = `https://api.x.com/oauth/authorize?oauth_token=${oauthToken.trim()}`
    
    const initialHeaders = {
      "authority": "api.x.com",
      "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
      "accept-language": "en-US,en;q=0.5",
      "accept-encoding": "gzip, deflate, br",
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
      "cookie": `auth_token=${twitterAuthToken.trim()}`,
      "referer": "https://x.com/"
    }
    
    const getResponse = await axios.get(authUrl, {
      headers: initialHeaders,
      timeout: 30000,
      maxRedirects: 5
    })
    
    console.log(`GET响应状态码: ${getResponse.status}`)
    
    // 提取authenticity_token
    let authenticityToken = null
    if (getResponse.data && getResponse.data.includes('authenticity_token')) {
      const tokenMatch = getResponse.data.match(/authenticity_token" value="([^"]+)"/);
      if (tokenMatch && tokenMatch[1]) {
        authenticityToken = tokenMatch[1]
        console.log(`✅ 找到authenticity_token: ${authenticityToken.substring(0, 20)}...`)
      }
    }
    
    // 提取CSRF token
    let csrfToken = null
    if (getResponse.headers['set-cookie']) {
      const cookies = getResponse.headers['set-cookie']
      for (const cookie of cookies) {
        if (cookie.includes('ct0=')) {
          const csrfMatch = cookie.match(/ct0=([^;]+)/)
          if (csrfMatch && csrfMatch[1]) {
            csrfToken = csrfMatch[1]
            console.log(`✅ 找到CSRF token: ${csrfToken.substring(0, 20)}...`)
            break
          }
        }
      }
    }
    
    if (!authenticityToken || !csrfToken) {
      console.log('❌ 无法获取必要的tokens')
      return
    }
    
    // 第二步：发送授权请求
    console.log('\n2. 发送OAuth授权请求...')
    
    const postData = new URLSearchParams({
      'authenticity_token': authenticityToken,
      'redirect_after_login': authUrl,
      'oauth_token': oauthToken.trim()
    })
    
    const postHeaders = {
      "authority": "api.x.com",
      "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
      "accept-language": "en-US,en;q=0.5",
      "accept-encoding": "gzip, deflate, br",
      "content-type": "application/x-www-form-urlencoded",
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
      "cookie": `auth_token=${twitterAuthToken.trim()}; ct0=${csrfToken}`,
      "x-csrf-token": csrfToken,
      "referer": authUrl,
      "origin": "https://api.x.com"
    }
    
    console.log('POST请求详情:')
    console.log(`  URL: https://api.x.com/oauth/authorize`)
    console.log(`  authenticity_token: ${authenticityToken.substring(0, 20)}...`)
    console.log(`  csrf_token: ${csrfToken.substring(0, 20)}...`)
    console.log(`  oauth_token: ${oauthToken.trim()}`)
    
    try {
      const postResponse = await axios.post(
        'https://api.x.com/oauth/authorize',
        postData,
        {
          headers: postHeaders,
          timeout: 30000,
          maxRedirects: 5,
          validateStatus: status => status >= 200 && status < 500
        }
      )
      
      console.log(`\nPOST响应状态码: ${postResponse.status}`)
      
      const responseText = typeof postResponse.data === 'string' ? postResponse.data : JSON.stringify(postResponse.data)
      console.log(`响应长度: ${responseText.length} 字符`)
      
      // 查找oauth_verifier
      if (responseText.includes('oauth_verifier')) {
        console.log('✅ 响应中包含oauth_verifier')
        
        const verifierMatch = responseText.match(/oauth_verifier=([^"&]+)/);
        if (verifierMatch && verifierMatch[1]) {
          const oauthVerifier = verifierMatch[1]
          console.log(`🎉 成功获取OAuth Verifier: ${oauthVerifier}`)
          
          // 第三步：测试回调处理
          console.log('\n3. 测试回调处理...')
          
          const callbackUrl = `https://api-theoriq.bonusblock.io/oauth/callback/twitter?client=theoriq&returnTo=https%3A%2F%2Fquests.theoriq.ai%2Fquests&oauth_token=${oauthToken.trim()}&oauth_verifier=${oauthVerifier}`
          
          console.log(`回调URL: ${callbackUrl}`)
          
          try {
            const callbackResponse = await axios.get(callbackUrl, {
              timeout: 30000,
              maxRedirects: 10,
              validateStatus: status => status >= 200 && status < 400
            })
            
            console.log(`✅ 回调处理成功`)
            console.log(`   状态码: ${callbackResponse.status}`)
            console.log(`   最终URL: ${callbackResponse.request?.res?.responseUrl || 'N/A'}`)
            
            return {
              success: true,
              oauthVerifier: oauthVerifier,
              authenticityToken: authenticityToken,
              callbackSuccess: true,
              finalUrl: callbackResponse.request?.res?.responseUrl
            }
            
          } catch (callbackError) {
            console.log(`⚠️  回调处理失败: ${callbackError.message}`)
            
            return {
              success: true,
              oauthVerifier: oauthVerifier,
              authenticityToken: authenticityToken,
              callbackSuccess: false,
              callbackError: callbackError.message
            }
          }
          
        } else {
          console.log('❌ 无法解析oauth_verifier')
        }
      } else {
        console.log('❌ 响应中不包含oauth_verifier')
        
        // 保存响应以便调试
        fs.writeFileSync('./oauth_auth_response.html', responseText)
        console.log('📄 响应已保存到 oauth_auth_response.html')
        
        // 检查是否需要用户交互
        if (responseText.includes('Authorize') || responseText.includes('authorize')) {
          console.log('⚠️  响应包含授权页面，可能需要用户手动点击授权')
        }
      }
      
    } catch (postError) {
      console.log(`❌ POST请求失败: ${postError.message}`)
      if (postError.response) {
        console.log(`   状态码: ${postError.response.status}`)
        console.log(`   响应: ${JSON.stringify(postError.response.data)}`)
      }
    }
    
  } catch (error) {
    console.error('测试过程中发生错误:', error.message)
  }
}

// 主程序
if (require.main === module) {
  testCSRFFix()
}

module.exports = { testCSRFFix }
