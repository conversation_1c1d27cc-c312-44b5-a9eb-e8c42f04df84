const SosovalueTwitterBinder = require('./sosovalue-twitter-bind');

/**
 * 测试真实的OAuth流程
 */
class RealOAuthTest {
    constructor() {
        this.binder = new SosovalueTwitterBinder();
    }

    /**
     * 测试完整的真实OAuth流程
     */
    async testRealOAuthFlow() {
        console.log('🚀 开始真实OAuth流程测试\n');
        
        try {
            // 1. 初始化钱包
            console.log('=== 步骤1: 初始化钱包 ===');
            const initResult = await this.binder.initWallet();
            if (!initResult.success) {
                throw new Error('钱包初始化失败');
            }
            console.log('✅ 钱包初始化成功');
            
            await this.delay(2000);
            
            // 2. 用户登录
            console.log('\n=== 步骤2: 用户登录 ===');
            const loginResult = await this.binder.performLogin();
            if (!loginResult.success) {
                throw new Error('用户登录失败');
            }
            console.log('✅ 用户登录成功');
            
            await this.delay(2000);
            
            // 3. 获取Twitter授权URL
            console.log('\n=== 步骤3: 获取Twitter授权URL ===');
            const authUrlResult = await this.binder.sosovalue.getTwitterAuthUrl(this.binder.token);
            
            if (!authUrlResult.success) {
                throw new Error(`获取Twitter授权URL失败: ${authUrlResult.msg}`);
            }
            
            console.log('✅ Twitter授权URL获取成功');
            console.log(`   授权URL: ${authUrlResult.authUrl}`);
            
            // 提取oauth_token
            const url = new URL(authUrlResult.authUrl);
            const oauthToken = url.searchParams.get('oauth_token');
            
            if (!oauthToken) {
                throw new Error('无法从授权URL中提取oauth_token');
            }
            
            console.log(`   OAuth Token: ${oauthToken}`);
            
            await this.delay(2000);
            
            // 4. 测试Twitter OAuth授权
            console.log('\n=== 步骤4: 测试Twitter OAuth授权 ===');
            console.log('正在尝试自动OAuth授权...');
            
            const oauthResult = await this.binder.twitter.getOAuthVerifier(oauthToken);
            
            if (oauthResult.success) {
                console.log('🎉 Twitter OAuth授权成功!');
                console.log(`   OAuth Verifier: ${oauthResult.oauthVerifier}`);
                
                await this.delay(2000);
                
                // 5. 完成Twitter绑定
                console.log('\n=== 步骤5: 完成Twitter绑定 ===');
                console.log('正在调用Sosovalue绑定API...');
                
                const bindResult = await this.binder.sosovalue.bindTwitter(
                    this.binder.token, 
                    oauthToken, 
                    oauthResult.oauthVerifier
                );
                
                if (bindResult.success) {
                    console.log('\n🎉🎉🎉 Twitter账号绑定完全成功! 🎉🎉🎉');
                    console.log(`✅ ${bindResult.msg}`);
                    
                    console.log('\n' + '='.repeat(60));
                    console.log('🏆 完整流程测试成功!');
                    console.log('='.repeat(60));
                    console.log('✅ 钱包初始化');
                    console.log('✅ 用户登录');
                    console.log('✅ Twitter授权URL获取');
                    console.log('✅ Twitter OAuth授权');
                    console.log('✅ Twitter账号绑定');
                    console.log('\n🎯 所有步骤都成功完成，Twitter账号已绑定到Sosovalue!');
                    
                } else {
                    console.log('❌ Twitter绑定失败');
                    console.log(`   错误: ${bindResult.msg}`);
                    console.log('\n但是OAuth授权成功了，这证明我们的修复是有效的!');
                }
                
            } else {
                console.log('⚠️  Twitter OAuth授权失败');
                console.log(`   错误: ${oauthResult.error}`);
                
                // 分析失败原因
                console.log('\n🔍 失败原因分析:');
                if (oauthResult.error.includes('404')) {
                    console.log('   - 404错误可能是因为授权端点已更改');
                    console.log('   - 或者需要不同的授权方法');
                } else if (oauthResult.error.includes('403')) {
                    console.log('   - 403错误可能是因为CSRF token问题');
                    console.log('   - 或者Twitter auth token已过期');
                } else {
                    console.log(`   - 其他错误: ${oauthResult.error}`);
                }
                
                console.log('\n💡 建议:');
                console.log('   - 检查Twitter auth token是否有效');
                console.log('   - 尝试手动完成授权流程');
                console.log('   - 检查网络连接和代理设置');
            }
            
        } catch (error) {
            console.log('\n' + '='.repeat(60));
            console.log('❌ 真实OAuth流程测试失败!');
            console.log('='.repeat(60));
            console.error(`错误: ${error.message}`);
        }
    }

    /**
     * 延迟函数
     */
    async delay(ms) {
        console.log(`⏳ 等待 ${ms/1000} 秒...`);
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 主程序
if (require.main === module) {
    const test = new RealOAuthTest();
    test.testRealOAuthFlow();
}

module.exports = RealOAuthTest;
