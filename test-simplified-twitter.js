const TwitterOAuth = require('./x/twitter');
const fs = require('fs');

/**
 * 测试简化后的Twitter OAuth类
 */
class SimplifiedTwitterTest {
    constructor() {
        this.twitter = null;
        this.twitterAuthToken = null;
    }

    /**
     * 初始化测试
     */
    async init() {
        try {
            console.log('=== 初始化简化Twitter OAuth测试 ===');
            
            // 从CSV文件读取Twitter auth token
            const csvContent = fs.readFileSync('./twitter-bindings-example.csv', 'utf8');
            const lines = csvContent.trim().split('\n');
            const [walletIndex, twitterAuthToken] = lines[1].split(',');
            
            this.twitterAuthToken = twitterAuthToken.trim();
            this.twitter = new TwitterOAuth(this.twitterAuthToken);
            
            console.log(`✅ 初始化成功`);
            console.log(`   Twitter Auth Token: ${this.twitterAuthToken.substring(0, 10)}...`);
            
            return { success: true };
            
        } catch (error) {
            console.error(`❌ 初始化失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 测试获取Twitter tokens
     */
    async testGetTokens() {
        try {
            console.log('\n=== 测试获取Twitter Tokens ===');
            
            const testOauthToken = 'test_oauth_token_123';
            console.log(`测试OAuth Token: ${testOauthToken}`);
            
            const result = await this.twitter.getTwitterTokens(testOauthToken);
            
            if (result) {
                console.log('✅ 获取tokens成功');
                const state = this.twitter.getState();
                console.log(`   Authenticity Token: ${state.authenticityToken ? '已获取' : '未获取'}`);
                console.log(`   CSRF Token: ${state.csrfToken ? '已获取' : '未获取'}`);
                return { success: true };
            } else {
                console.log('❌ 获取tokens失败');
                return { success: false };
            }
            
        } catch (error) {
            console.error(`❌ 测试获取tokens失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 测试OAuth授权流程
     */
    async testOAuthAuthorization() {
        try {
            console.log('\n=== 测试OAuth授权流程 ===');
            
            const testOauthToken = 'test_oauth_token_123';
            console.log(`开始OAuth授权测试...`);
            
            const result = await this.twitter.authorizeWithTwitter(testOauthToken);
            
            console.log(`授权结果: ${result.success ? '成功' : '失败'}`);
            if (result.success) {
                console.log(`✅ OAuth授权成功`);
                console.log(`   OAuth Verifier: ${result.oauthVerifier?.substring(0, 20)}...`);
            } else {
                console.log(`⚠️  OAuth授权失败: ${result.error}`);
                console.log(`   这是预期的，因为使用的是测试token`);
            }
            
            return result;
            
        } catch (error) {
            console.error(`❌ 测试OAuth授权失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 测试公共接口
     */
    async testPublicInterface() {
        try {
            console.log('\n=== 测试公共接口 ===');
            
            const testOauthToken = 'test_oauth_token_123';
            
            // 测试getOAuthVerifier方法
            console.log('测试getOAuthVerifier方法...');
            const verifierResult = await this.twitter.getOAuthVerifier(testOauthToken);
            
            console.log(`getOAuthVerifier结果: ${verifierResult.success ? '成功' : '失败'}`);
            if (verifierResult.success) {
                console.log(`   OAuth Verifier: ${verifierResult.oauthVerifier?.substring(0, 20)}...`);
            } else {
                console.log(`   错误: ${verifierResult.error}`);
            }
            
            // 测试状态管理
            console.log('\n测试状态管理...');
            const state = this.twitter.getState();
            console.log(`状态属性数量: ${Object.keys(state).length} (应该是3个)`);
            console.log(`状态属性: ${Object.keys(state).join(', ')}`);
            
            // 测试重置
            this.twitter.resetState();
            const resetState = this.twitter.getState();
            const allNull = Object.values(resetState).every(value => value === null);
            console.log(`重置状态: ${allNull ? '成功' : '失败'}`);
            
            return { success: true };
            
        } catch (error) {
            console.error(`❌ 测试公共接口失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 测试兼容性方法
     */
    async testCompatibility() {
        try {
            console.log('\n=== 测试兼容性方法 ===');
            
            const testOauthToken = 'test_oauth_token_123';
            
            // 测试兼容性方法
            console.log('测试兼容性方法...');
            
            // 先获取tokens
            await this.twitter.getTwitterTokens(testOauthToken);
            
            // 测试兼容性getter方法
            const authenticityToken = this.twitter.getAuthenticityToken();
            const oauthVerifier = this.twitter.getOauthVerifier();
            
            console.log(`getAuthenticityToken: ${authenticityToken ? '有值' : '无值'}`);
            console.log(`getOauthVerifier: ${oauthVerifier ? '有值' : '无值'}`);
            
            // 测试兼容性重置方法
            this.twitter.resetTokens();
            const afterReset = this.twitter.getAuthenticityToken();
            console.log(`resetTokens后: ${afterReset ? '仍有值' : '已清空'}`);
            
            // 测试twitterAuthorize方法
            const authResult = await this.twitter.twitterAuthorize(testOauthToken);
            console.log(`twitterAuthorize: ${authResult ? '成功' : '失败'}`);
            
            return { success: true };
            
        } catch (error) {
            console.error(`❌ 测试兼容性失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        console.log('🚀 开始简化Twitter OAuth类测试\n');
        
        try {
            // 初始化
            const initResult = await this.init();
            if (!initResult.success) {
                throw new Error('初始化失败');
            }
            
            // 测试获取tokens
            await this.testGetTokens();
            await this.delay(1000);
            
            // 测试OAuth授权
            await this.testOAuthAuthorization();
            await this.delay(1000);
            
            // 测试公共接口
            await this.testPublicInterface();
            await this.delay(1000);
            
            // 测试兼容性
            await this.testCompatibility();
            
            console.log('\n' + '='.repeat(60));
            console.log('🎉 简化Twitter OAuth类测试完成!');
            console.log('='.repeat(60));
            console.log('✅ 类简化成功');
            console.log('✅ 核心方法正常工作');
            console.log('✅ 状态管理简化');
            console.log('✅ 兼容性保持');
            
            console.log('\n📋 简化后的核心方法:');
            console.log('   - getTwitterTokens(oauthToken)     # 获取认证tokens');
            console.log('   - authorizeWithTwitter(oauthToken) # 执行OAuth授权');
            console.log('   - getOAuthVerifier(oauthToken)     # 主要公共接口');
            console.log('   - getState() / resetState()        # 状态管理');
            
            console.log('\n📋 兼容性方法:');
            console.log('   - getTwitterToken(oauthToken)      # 兼容原方法');
            console.log('   - twitterAuthorize(oauthToken)     # 兼容原方法');
            console.log('   - getAuthenticityToken()           # 兼容getter');
            console.log('   - getOauthVerifier()               # 兼容getter');
            console.log('   - resetTokens()                    # 兼容重置');
            
        } catch (error) {
            console.log('\n' + '='.repeat(60));
            console.log('❌ 测试失败!');
            console.log('='.repeat(60));
            console.error(`错误: ${error.message}`);
        }
    }

    /**
     * 延迟函数
     */
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 主程序
if (require.main === module) {
    const test = new SimplifiedTwitterTest();
    test.runAllTests();
}

module.exports = SimplifiedTwitterTest;
