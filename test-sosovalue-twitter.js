const ethers = require('ethers');
const Sosovalue = require('./sosovalue/sosovalue');
const fs = require('fs');

/**
 * Sosovalue Twitter绑定完整测试脚本
 */
class SosovalueTwitterTest {
    constructor() {
        this.wallet = null;
        this.sosovalue = null;
        this.token = null;
        this.userId = null;
        this.username = null;
    }

    /**
     * 初始化钱包
     */
    async initWallet() {
        try {
            console.log('=== 步骤1: 初始化钱包 ===');
            
            // 从CSV文件读取钱包信息
            const csvContent = fs.readFileSync('./twitter-bindings-example.csv', 'utf8');
            const lines = csvContent.trim().split('\n');
            
            if (lines.length < 2) {
                throw new Error('CSV文件格式错误或数据为空');
            }
            
            // 解析CSV数据
            const [walletIndex, twitterAuthToken] = lines[1].split(',');
            
            // 从wallets.csv读取钱包私钥
            const walletsCsvContent = fs.readFileSync('./wallets.csv', 'utf8');
            const walletLines = walletsCsvContent.trim().split('\n');
            
            let privateKey = null;
            for (let i = 1; i < walletLines.length; i++) {
                const [index, addr, pk] = walletLines[i].split(',');
                if (index === walletIndex) {
                    privateKey = pk.trim();
                    break;
                }
            }
            
            if (!privateKey) {
                throw new Error(`未找到钱包索引 ${walletIndex} 的私钥`);
            }
            
            // 创建钱包实例
            this.wallet = new ethers.Wallet(privateKey);
            this.sosovalue = new Sosovalue(this.wallet);
            
            console.log(`✅ 钱包初始化成功`);
            console.log(`   地址: ${this.wallet.address}`);
            console.log(`   Twitter Auth Token: ${twitterAuthToken.substring(0, 10)}...`);
            
            return { success: true, twitterAuthToken };
            
        } catch (error) {
            console.error(`❌ 钱包初始化失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 获取CF Token
     */
    async getCFToken() {
        try {
            console.log('\n=== 步骤2: 获取CF Token ===');
            console.log('正在获取Cloudflare防护token...');
            
            const cfToken = await this.sosovalue.getLocalCaptcha();
            
            console.log('✅ CF Token获取成功');
            console.log(`   Token: ${cfToken.substring(0, 20)}...`);
            
            return { success: true, cfToken };
            
        } catch (error) {
            console.error(`❌ CF Token获取失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 用户登录
     */
    async login(cfToken, inviteCode = null) {
        try {
            console.log('\n=== 步骤3: 用户登录 ===');
            console.log('正在进行用户登录...');
            
            const loginResult = await this.sosovalue.login(inviteCode, cfToken);
            
            if (loginResult.success) {
                this.token = loginResult.token;
                this.userId = loginResult.userId;
                this.username = loginResult.username;
                
                console.log('✅ 用户登录成功');
                console.log(`   Token: ${this.token.substring(0, 20)}...`);
                console.log(`   User ID: ${this.userId}`);
                console.log(`   Username: ${this.username}`);
                console.log(`   过期时间: ${loginResult.expiration}`);
                
                return { success: true, loginData: loginResult };
            } else {
                throw new Error(loginResult.msg);
            }
            
        } catch (error) {
            console.error(`❌ 用户登录失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 获取Twitter授权URL
     */
    async getTwitterAuthUrl() {
        try {
            console.log('\n=== 步骤4: 获取Twitter授权URL ===');
            console.log('正在获取Twitter OAuth授权链接...');
            
            const authUrlResult = await this.sosovalue.getTwitterAuthUrl(this.token);
            
            if (authUrlResult.success) {
                console.log('✅ Twitter授权URL获取成功');
                console.log(`   授权URL: ${authUrlResult.authUrl}`);
                
                // 从URL中提取oauth_token
                const url = new URL(authUrlResult.authUrl);
                const oauthToken = url.searchParams.get('oauth_token');
                
                if (oauthToken) {
                    console.log(`   OAuth Token: ${oauthToken}`);
                    return { success: true, authUrl: authUrlResult.authUrl, oauthToken };
                } else {
                    throw new Error('无法从授权URL中提取oauth_token');
                }
            } else {
                throw new Error(authUrlResult.msg);
            }
            
        } catch (error) {
            console.error(`❌ 获取Twitter授权URL失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 处理Twitter授权（模拟或手动）
     */
    async handleTwitterAuth(authUrl, oauthToken) {
        try {
            console.log('\n=== 步骤5: 处理Twitter授权 ===');
            console.log('⚠️  需要手动完成Twitter授权流程');
            console.log('');
            console.log('请按以下步骤操作:');
            console.log('1. 在浏览器中打开以下URL:');
            console.log(`   ${authUrl}`);
            console.log('');
            console.log('2. 使用您的Twitter账号登录并授权应用');
            console.log('');
            console.log('3. 授权完成后，您将被重定向到回调URL');
            console.log('   回调URL格式类似: https://sosovalue.com?oauth_token=XXX&oauth_verifier=YYY');
            console.log('');
            console.log('4. 从回调URL中复制 oauth_verifier 参数的值');
            console.log('');
            
            // 模拟获取oauth_verifier（实际使用时需要手动输入）
            console.log('💡 对于测试目的，我们将模拟一个oauth_verifier');
            const mockOauthVerifier = 'mock_oauth_verifier_' + Date.now();
            
            console.log(`✅ 模拟获取OAuth Verifier: ${mockOauthVerifier}`);
            
            return { 
                success: true, 
                oauthToken, 
                oauthVerifier: mockOauthVerifier,
                isSimulated: true
            };
            
        } catch (error) {
            console.error(`❌ Twitter授权处理失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 完成Twitter绑定
     */
    async bindTwitter(oauthToken, oauthVerifier, isSimulated = false) {
        try {
            console.log('\n=== 步骤6: 完成Twitter绑定 ===');
            
            if (isSimulated) {
                console.log('⚠️  使用模拟的oauth_verifier进行测试');
                console.log('   实际使用时请替换为真实的oauth_verifier');
            }
            
            console.log('正在绑定Twitter账号...');
            console.log(`   OAuth Token: ${oauthToken}`);
            console.log(`   OAuth Verifier: ${oauthVerifier}`);
            
            const bindResult = await this.sosovalue.bindTwitter(this.token, oauthToken, oauthVerifier);
            
            if (bindResult.success) {
                console.log('✅ Twitter绑定成功!');
                console.log(`   消息: ${bindResult.msg}`);
                return { success: true, bindData: bindResult };
            } else {
                // 如果是模拟数据导致的失败，这是预期的
                if (isSimulated) {
                    console.log('⚠️  绑定失败（预期结果，因为使用了模拟数据）');
                    console.log(`   错误信息: ${bindResult.msg}`);
                    console.log('   使用真实的oauth_verifier应该可以成功绑定');
                    return { success: false, error: bindResult.msg, expected: true };
                } else {
                    throw new Error(bindResult.msg);
                }
            }
            
        } catch (error) {
            console.error(`❌ Twitter绑定失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 运行完整测试流程
     */
    async runFullTest() {
        console.log('🚀 开始Sosovalue Twitter绑定完整测试\n');
        
        try {
            // 步骤1: 初始化钱包
            const walletResult = await this.initWallet();
            if (!walletResult.success) {
                throw new Error('钱包初始化失败');
            }
            
            // 添加延迟
            await this.delay(2000);
            
            // 步骤2: 获取CF Token
            const cfTokenResult = await this.getCFToken();
            if (!cfTokenResult.success) {
                throw new Error('CF Token获取失败');
            }
            
            // 添加延迟
            await this.delay(2000);
            
            // 步骤3: 用户登录
            const loginResult = await this.login(cfTokenResult.cfToken);
            if (!loginResult.success) {
                throw new Error('用户登录失败');
            }
            
            // 添加延迟
            await this.delay(2000);
            
            // 步骤4: 获取Twitter授权URL
            const authUrlResult = await this.getTwitterAuthUrl();
            if (!authUrlResult.success) {
                throw new Error('获取Twitter授权URL失败');
            }
            
            // 添加延迟
            await this.delay(2000);
            
            // 步骤5: 处理Twitter授权
            const authResult = await this.handleTwitterAuth(authUrlResult.authUrl, authUrlResult.oauthToken);
            if (!authResult.success) {
                throw new Error('Twitter授权处理失败');
            }
            
            // 添加延迟
            await this.delay(2000);
            
            // 步骤6: 完成Twitter绑定
            const bindResult = await this.bindTwitter(authResult.oauthToken, authResult.oauthVerifier, authResult.isSimulated);
            
            // 显示最终结果
            console.log('\n' + '='.repeat(50));
            console.log('🎉 测试流程完成!');
            console.log('='.repeat(50));
            
            if (bindResult.success) {
                console.log('✅ 所有步骤都成功完成');
                console.log('✅ Twitter账号绑定成功');
            } else if (bindResult.expected) {
                console.log('✅ 所有步骤都正常执行');
                console.log('⚠️  最后一步因使用模拟数据而失败（这是预期的）');
                console.log('💡 使用真实的oauth_verifier可以完成实际绑定');
            } else {
                console.log('❌ 绑定步骤失败');
                console.log(`   错误: ${bindResult.error}`);
            }
            
            console.log('\n📋 测试总结:');
            console.log('   ✅ 钱包初始化');
            console.log('   ✅ CF Token获取');
            console.log('   ✅ 用户登录');
            console.log('   ✅ Twitter授权URL获取');
            console.log('   ✅ Twitter授权处理');
            console.log(`   ${bindResult.success ? '✅' : '⚠️ '} Twitter绑定${bindResult.expected ? '（模拟）' : ''}`);
            
        } catch (error) {
            console.log('\n' + '='.repeat(50));
            console.log('❌ 测试流程失败!');
            console.log('='.repeat(50));
            console.error(`错误: ${error.message}`);
        }
    }

    /**
     * 延迟函数
     */
    async delay(ms) {
        console.log(`⏳ 等待 ${ms/1000} 秒...`);
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 主程序
if (require.main === module) {
    const test = new SosovalueTwitterTest();
    test.runFullTest();
}

module.exports = SosovalueTwitterTest;
