const fs = require('fs')
const path = require('path')
const theoriqManager = require('./TheoriqManager')
const { bindTwitter } = require('./tasks/twitter-bind')
const logger = require('./utils/logger')

/**
 * 从CSV文件读取Twitter绑定数据
 */
function readTwitterBindings() {
  try {
    const csvPath = path.join(__dirname, 'twitter-bindings-example.csv')
    const csvContent = fs.readFileSync(csvPath, 'utf8')
    const lines = csvContent.trim().split('\n')
    
    if (lines.length < 2) {
      throw new Error('CSV文件格式错误或数据为空')
    }
    
    const headers = lines[0].split(',')
    const bindings = []
    
    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',')
      if (values.length >= 3) {
        bindings.push({
          walletIndex: parseInt(values[0]),
          twitterAuthToken: values[1].trim(),
          oauthToken: values[2].trim()
        })
      }
    }
    
    return bindings
  } catch (error) {
    logger.error('读取Twitter绑定数据失败:', error.message)
    return []
  }
}

/**
 * 测试单个Twitter绑定
 */
async function testSingleBinding(binding) {
  console.log(`\n=== 测试钱包 ${binding.walletIndex} 的Twitter绑定 ===`)
  
  // 检查数据完整性
  if (binding.oauthToken === 'your_oauth_token_here') {
    console.log('❌ 请先在 twitter-bindings-example.csv 中设置真实的 oauthToken')
    console.log('   您需要从Theoriq API获取oauth_token')
    return { success: false, error: 'OAuth token未设置' }
  }
  
  if (!binding.twitterAuthToken || binding.twitterAuthToken.length < 10) {
    console.log('❌ Twitter auth_token无效')
    return { success: false, error: 'Twitter auth_token无效' }
  }
  
  console.log(`钱包索引: ${binding.walletIndex}`)
  console.log(`Twitter Auth Token: ${binding.twitterAuthToken.substring(0, 10)}...`)
  console.log(`OAuth Token: ${binding.oauthToken}`)
  
  try {
    // 执行Twitter绑定
    const result = await bindTwitter(
      binding.walletIndex,
      binding.twitterAuthToken,
      binding.oauthToken,
      true // 处理回调
    )
    
    if (result.success) {
      console.log('✅ Twitter绑定成功!')
      console.log(`   OAuth Verifier: ${result.data.oauthVerifier?.substring(0, 15)}...`)
      console.log(`   Authenticity Token: ${result.data.authenticityToken?.substring(0, 15)}...`)
      
      if (result.data.callback) {
        console.log(`   回调处理: ${result.data.callback.success ? '成功' : '失败'}`)
        if (result.data.callback.success) {
          console.log(`   最终URL: ${result.data.callback.finalUrl || 'N/A'}`)
        } else {
          console.log(`   回调错误: ${result.data.callback.error || 'N/A'}`)
        }
      }
      
      return result
    } else {
      console.log('❌ Twitter绑定失败')
      console.log(`   错误: ${result.error}`)
      return result
    }
    
  } catch (error) {
    console.log('❌ 绑定过程中发生异常')
    console.log(`   错误: ${error.message}`)
    return { success: false, error: error.message }
  }
}

/**
 * 测试所有Twitter绑定
 */
async function testAllBindings() {
  console.log('=== Twitter绑定批量测试 ===\n')
  
  const bindings = readTwitterBindings()
  
  if (bindings.length === 0) {
    console.log('❌ 没有找到有效的Twitter绑定数据')
    console.log('请检查 twitter-bindings-example.csv 文件')
    return
  }
  
  console.log(`找到 ${bindings.length} 个Twitter绑定配置`)
  
  const results = []
  
  for (const binding of bindings) {
    const result = await testSingleBinding(binding)
    results.push({
      walletIndex: binding.walletIndex,
      ...result
    })
    
    // 添加延迟避免请求过快
    if (bindings.length > 1) {
      console.log('\n等待3秒后继续下一个...')
      await new Promise(resolve => setTimeout(resolve, 3000))
    }
  }
  
  // 显示总结
  console.log('\n=== 测试总结 ===')
  const successful = results.filter(r => r.success).length
  const failed = results.filter(r => !r.success).length
  
  console.log(`总计: ${results.length} 个绑定`)
  console.log(`成功: ${successful} 个`)
  console.log(`失败: ${failed} 个`)
  
  if (failed > 0) {
    console.log('\n失败详情:')
    results.filter(r => !r.success).forEach(r => {
      console.log(`  钱包 ${r.walletIndex}: ${r.error}`)
    })
  }
}

/**
 * 显示使用说明
 */
function showUsage() {
  console.log('=== Twitter绑定测试使用说明 ===\n')
  
  console.log('1. 准备数据:')
  console.log('   - 确保 wallets.csv 中有钱包数据')
  console.log('   - 在 twitter-bindings-example.csv 中设置Twitter数据\n')
  
  console.log('2. 获取Twitter auth_token:')
  console.log('   - 登录 x.com')
  console.log('   - 开发者工具 -> Application -> Cookies -> auth_token\n')
  
  console.log('3. 获取OAuth token:')
  console.log('   - 从Theoriq API获取oauth_token')
  console.log('   - 通常通过 /api/social/twitter/request-token 端点\n')
  
  console.log('4. 运行测试:')
  console.log('   node test-twitter-binding.js        # 测试所有绑定')
  console.log('   node test-twitter-binding.js help   # 显示帮助')
  console.log('   node test-twitter-binding.js check  # 检查配置\n')
}

/**
 * 检查配置
 */
function checkConfig() {
  console.log('=== 配置检查 ===\n')
  
  // 检查钱包文件
  try {
    const walletManager = require('./utils/WalletManager')
    const walletCount = walletManager.getWalletCount()
    console.log(`✅ 钱包文件: 找到 ${walletCount} 个钱包`)
  } catch (error) {
    console.log(`❌ 钱包文件错误: ${error.message}`)
  }
  
  // 检查Twitter绑定文件
  const bindings = readTwitterBindings()
  if (bindings.length > 0) {
    console.log(`✅ Twitter绑定文件: 找到 ${bindings.length} 个配置`)
    bindings.forEach(b => {
      const tokenValid = b.oauthToken !== 'your_oauth_token_here'
      console.log(`   钱包 ${b.walletIndex}: ${tokenValid ? '✅' : '❌'} OAuth Token ${tokenValid ? '已设置' : '未设置'}`)
    })
  } else {
    console.log('❌ Twitter绑定文件: 没有找到有效配置')
  }
  
  // 检查配置文件
  try {
    const config = require('./config.json')
    console.log(`✅ 配置文件: 代理${config.proxy?.enabled ? '已启用' : '已禁用'}`)
  } catch (error) {
    console.log(`❌ 配置文件错误: ${error.message}`)
  }
}

// 主程序
if (require.main === module) {
  const args = process.argv.slice(2)
  
  if (args[0] === 'help' || args[0] === '-h') {
    showUsage()
  } else if (args[0] === 'check') {
    checkConfig()
  } else {
    testAllBindings()
  }
}

module.exports = { testSingleBinding, testAllBindings, readTwitterBindings }
