const TwitterOAuth = require('./x/twitter');
const fs = require('fs');

/**
 * 测试重构后的Twitter OAuth类
 */
class TwitterOAuthTest {
    constructor() {
        this.twitterAuthToken = null;
        this.twitter = null;
    }

    /**
     * 初始化测试
     */
    async init() {
        try {
            console.log('=== 初始化Twitter OAuth测试 ===');
            
            // 从CSV文件读取Twitter auth token
            const csvContent = fs.readFileSync('./twitter-bindings-example.csv', 'utf8');
            const lines = csvContent.trim().split('\n');
            
            if (lines.length < 2) {
                throw new Error('CSV文件格式错误或数据为空');
            }
            
            const [walletIndex, twitterAuthToken] = lines[1].split(',');
            this.twitterAuthToken = twitterAuthToken.trim();
            
            // 创建Twitter OAuth实例
            this.twitter = new TwitterOAuth(this.twitterAuthToken);
            
            console.log(`✅ 初始化成功`);
            console.log(`   Twitter Auth Token: ${this.twitterAuthToken.substring(0, 10)}...`);
            
            return { success: true };
            
        } catch (error) {
            console.error(`❌ 初始化失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 测试获取Twitter tokens
     */
    async testGetTokens() {
        try {
            console.log('\n=== 测试获取Twitter Tokens ===');
            
            // 使用一个示例oauth_token进行测试
            const testOauthToken = 'test_oauth_token_123';
            
            console.log('正在获取Twitter tokens...');
            const result = await this.twitter.getTwitterTokens(testOauthToken);
            
            if (result) {
                console.log('✅ 获取tokens成功');
                const state = this.twitter.getState();
                console.log(`   Authenticity Token: ${state.authenticityToken?.substring(0, 20)}...`);
                console.log(`   CSRF Token: ${state.csrfToken?.substring(0, 20)}...`);
                return { success: true, state };
            } else {
                console.log('❌ 获取tokens失败');
                return { success: false };
            }
            
        } catch (error) {
            console.error(`❌ 测试获取tokens失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 测试OAuth授权流程
     */
    async testOAuthFlow() {
        try {
            console.log('\n=== 测试OAuth授权流程 ===');
            
            const testOauthToken = 'test_oauth_token_123';
            
            console.log('正在执行OAuth授权...');
            const result = await this.twitter.performOAuthAuthorization(testOauthToken);
            
            console.log(`授权结果: ${result.success ? '成功' : '失败'}`);
            if (result.success) {
                console.log(`   OAuth Verifier: ${result.oauthVerifier?.substring(0, 20)}...`);
            } else {
                console.log(`   错误: ${result.error}`);
            }
            
            return result;
            
        } catch (error) {
            console.error(`❌ 测试OAuth流程失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 测试新的公共接口
     */
    async testPublicInterface() {
        try {
            console.log('\n=== 测试公共接口 ===');
            
            const testOauthToken = 'test_oauth_token_123';
            
            // 测试getOAuthVerifier方法
            console.log('测试getOAuthVerifier方法...');
            const verifierResult = await this.twitter.getOAuthVerifier(testOauthToken);
            
            console.log(`getOAuthVerifier结果: ${verifierResult.success ? '成功' : '失败'}`);
            if (verifierResult.success) {
                console.log(`   OAuth Verifier: ${verifierResult.oauthVerifier?.substring(0, 20)}...`);
                console.log(`   OAuth Token: ${verifierResult.oauthToken}`);
            } else {
                console.log(`   错误: ${verifierResult.error}`);
            }
            
            // 测试状态管理
            console.log('\n测试状态管理...');
            const state = this.twitter.getState();
            console.log(`当前状态:`);
            console.log(`   Authenticity Token: ${state.authenticityToken ? '已设置' : '未设置'}`);
            console.log(`   OAuth Verifier: ${state.oauthVerifier ? '已设置' : '未设置'}`);
            console.log(`   CSRF Token: ${state.csrfToken ? '已设置' : '未设置'}`);
            
            // 测试重置状态
            console.log('\n测试重置状态...');
            this.twitter.resetState();
            const resetState = this.twitter.getState();
            const allNull = Object.values(resetState).every(value => value === null);
            console.log(`重置状态: ${allNull ? '成功' : '失败'}`);
            
            return { success: true };
            
        } catch (error) {
            console.error(`❌ 测试公共接口失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 测试兼容性方法
     */
    async testCompatibility() {
        try {
            console.log('\n=== 测试兼容性方法 ===');
            
            const testOauthToken = 'test_oauth_token_123';
            
            // 测试兼容性方法
            console.log('测试兼容性方法...');
            
            // 先获取tokens
            await this.twitter.getTwitterTokens(testOauthToken);
            
            // 测试兼容性getter方法
            const authenticityToken = this.twitter.getAuthenticityToken();
            const oauthVerifier = this.twitter.getOauthVerifier();
            
            console.log(`getAuthenticityToken: ${authenticityToken ? '有值' : '无值'}`);
            console.log(`getOauthVerifier: ${oauthVerifier ? '有值' : '无值'}`);
            
            // 测试兼容性重置方法
            this.twitter.resetTokens();
            const afterReset = this.twitter.getAuthenticityToken();
            console.log(`resetTokens后: ${afterReset ? '仍有值' : '已清空'}`);
            
            return { success: true };
            
        } catch (error) {
            console.error(`❌ 测试兼容性失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        console.log('🚀 开始Twitter OAuth类测试\n');
        
        try {
            // 初始化
            const initResult = await this.init();
            if (!initResult.success) {
                throw new Error('初始化失败');
            }
            
            // 测试获取tokens
            await this.testGetTokens();
            
            // 添加延迟
            await this.delay(2000);
            
            // 测试OAuth流程
            await this.testOAuthFlow();
            
            // 添加延迟
            await this.delay(2000);
            
            // 测试公共接口
            await this.testPublicInterface();
            
            // 添加延迟
            await this.delay(2000);
            
            // 测试兼容性
            await this.testCompatibility();
            
            console.log('\n' + '='.repeat(50));
            console.log('🎉 Twitter OAuth类测试完成!');
            console.log('='.repeat(50));
            console.log('✅ 类重构成功');
            console.log('✅ 新的公共接口可用');
            console.log('✅ 状态管理正常');
            console.log('✅ 兼容性方法正常');
            console.log('\n📋 可用的公共方法:');
            console.log('   - getOAuthVerifier(oauthToken)');
            console.log('   - performOAuthAuthorization(oauthToken)');
            console.log('   - getTwitterTokens(oauthToken)');
            console.log('   - getState()');
            console.log('   - resetState()');
            console.log('\n📋 兼容性方法:');
            console.log('   - getTwitterToken(oauthToken)');
            console.log('   - twitterAuthorize(oauthToken)');
            console.log('   - getAuthenticityToken()');
            console.log('   - getOauthVerifier()');
            console.log('   - resetTokens()');
            
        } catch (error) {
            console.log('\n' + '='.repeat(50));
            console.log('❌ 测试失败!');
            console.log('='.repeat(50));
            console.error(`错误: ${error.message}`);
        }
    }

    /**
     * 延迟函数
     */
    async delay(ms) {
        console.log(`⏳ 等待 ${ms/1000} 秒...`);
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 主程序
if (require.main === module) {
    const test = new TwitterOAuthTest();
    test.runAllTests();
}

module.exports = TwitterOAuthTest;
