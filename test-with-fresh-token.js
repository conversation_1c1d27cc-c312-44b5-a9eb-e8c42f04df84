const Twitter = require('./x/twitter')
const logger = require('./utils/logger')

/**
 * 使用新的OAuth token测试
 * 这个脚本会尝试不同格式的OAuth token
 */
async function testWithFreshToken() {
  try {
    console.log('=== 使用新OAuth Token测试 ===\n')
    
    // 从CSV读取Twitter auth_token
    const fs = require('fs')
    const csvContent = fs.readFileSync('./twitter-bindings-example.csv', 'utf8')
    const lines = csvContent.trim().split('\n')
    const [walletIndex, twitterAuthToken, currentOauthToken] = lines[1].split(',')
    
    console.log(`Twitter Auth Token: ${twitterAuthToken.substring(0, 10)}...`)
    console.log(`当前OAuth Token: ${currentOauthToken}`)
    
    // 创建Twitter实例
    const twitter = new Twitter(twitterAuthToken.trim())
    
    // 测试不同的OAuth token格式
    const testTokens = [
      currentOauthToken.trim(), // 当前token
      'UyK0UAAAAAABoeyUAAABl26malB', // 稍微修改的token
      'UyK0UAAAAAABoeyUAAABl26malC', // 另一个变体
      'test_oauth_token_123456789', // 测试token
    ]
    
    for (let i = 0; i < testTokens.length; i++) {
      const testToken = testTokens[i]
      console.log(`\n--- 测试Token ${i + 1}: ${testToken} ---`)
      
      try {
        // 测试获取authenticity_token
        const tokenResult = await twitter.getTwitterToken(testToken)
        
        if (tokenResult) {
          console.log('✅ 获取authenticity_token成功')
          console.log(`   Token: ${twitter.getAuthenticityToken()?.substring(0, 20)}...`)
          
          // 如果成功，尝试OAuth授权
          const authResult = await twitter.twitterAuthorize(testToken)
          
          if (authResult) {
            console.log('✅ OAuth授权成功!')
            console.log(`   OAuth Verifier: ${twitter.getOauthVerifier()?.substring(0, 20)}...`)
            
            // 测试回调
            const callbackResult = await twitter.handleOAuthCallback(
              testToken,
              twitter.getOauthVerifier()
            )
            
            if (callbackResult.success) {
              console.log('✅ 回调处理成功!')
              console.log(`   状态码: ${callbackResult.status}`)
              console.log(`   最终URL: ${callbackResult.finalUrl || 'N/A'}`)
              
              // 更新CSV文件
              const newCsvContent = `walletIndex,twitterAuthToken,oauthToken\n${walletIndex},${twitterAuthToken},${testToken}`
              fs.writeFileSync('./twitter-bindings-example.csv', newCsvContent)
              console.log('✅ 已更新CSV文件')
              
              return { success: true, token: testToken }
            } else {
              console.log('❌ 回调处理失败')
              console.log(`   错误: ${callbackResult.error}`)
            }
          } else {
            console.log('❌ OAuth授权失败')
          }
        } else {
          console.log('❌ 获取authenticity_token失败')
        }
        
        // 重置tokens为下次测试准备
        twitter.resetTokens()
        
      } catch (error) {
        console.log(`❌ 测试失败: ${error.message}`)
        twitter.resetTokens()
      }
      
      // 添加延迟避免请求过快
      if (i < testTokens.length - 1) {
        console.log('等待2秒后继续...')
        await new Promise(resolve => setTimeout(resolve, 2000))
      }
    }
    
    console.log('\n=== 所有测试完成 ===')
    console.log('如果所有token都失败，可能需要：')
    console.log('1. 从Theoriq平台手动获取新的OAuth token')
    console.log('2. 检查Twitter auth_token是否仍然有效')
    console.log('3. 检查网络连接和代理设置')
    
  } catch (error) {
    console.error('测试过程中发生错误:', error.message)
  }
}

/**
 * 生成新的测试OAuth token
 */
function generateTestOAuthToken() {
  // OAuth token通常是Base64编码的字符串
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2)
  const testToken = `test_${timestamp}_${random}`
  
  console.log('=== 生成测试OAuth Token ===\n')
  console.log(`生成的测试Token: ${testToken}`)
  console.log('\n注意：这只是一个测试token，可能不会工作')
  console.log('真实的OAuth token需要从Theoriq API获取')
  
  return testToken
}

/**
 * 直接测试Twitter绑定功能
 */
async function testDirectBinding() {
  try {
    console.log('=== 直接测试Twitter绑定 ===\n')
    
    const { bindTwitter } = require('./tasks/twitter-bind')
    
    // 从CSV读取配置
    const fs = require('fs')
    const csvContent = fs.readFileSync('./twitter-bindings-example.csv', 'utf8')
    const lines = csvContent.trim().split('\n')
    const [walletIndex, twitterAuthToken, oauthToken] = lines[1].split(',')
    
    console.log('开始Twitter绑定测试...')
    
    // 执行绑定（不处理回调，避免可能的错误）
    const result = await bindTwitter(
      parseInt(walletIndex),
      twitterAuthToken.trim(),
      oauthToken.trim(),
      false // 不处理回调
    )
    
    if (result.success) {
      console.log('✅ Twitter绑定成功!')
      console.log(`   OAuth Verifier: ${result.data.oauthVerifier?.substring(0, 15)}...`)
      console.log(`   Authenticity Token: ${result.data.authenticityToken?.substring(0, 15)}...`)
    } else {
      console.log('❌ Twitter绑定失败')
      console.log(`   错误: ${result.error}`)
    }
    
    return result
    
  } catch (error) {
    console.error('直接绑定测试失败:', error.message)
    return { success: false, error: error.message }
  }
}

// 主程序
if (require.main === module) {
  const args = process.argv.slice(2)
  
  if (args[0] === 'generate') {
    generateTestOAuthToken()
  } else if (args[0] === 'direct') {
    testDirectBinding()
  } else {
    testWithFreshToken()
  }
}

module.exports = { testWithFreshToken, testDirectBinding }
