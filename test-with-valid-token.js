const TwitterOAuth = require('./x/twitter');
const readline = require('readline');

/**
 * 测试使用有效Twitter token的OAuth流程
 */
class ValidTokenTest {
    constructor() {
        this.twitter = null;
    }

    /**
     * 创建readline接口
     */
    createReadlineInterface() {
        return readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
    }

    /**
     * 获取用户输入
     */
    async getUserInput(question) {
        const rl = this.createReadlineInterface();
        return new Promise((resolve) => {
            rl.question(question, (answer) => {
                rl.close();
                resolve(answer.trim());
            });
        });
    }

    /**
     * 显示获取Twitter auth token的说明
     */
    showTokenInstructions() {
        console.log('=== 如何获取有效的Twitter Auth Token ===\n');
        console.log('1. 打开浏览器，访问 https://x.com (Twitter)');
        console.log('2. 登录您的Twitter账号');
        console.log('3. 按F12打开开发者工具');
        console.log('4. 转到 Application 标签');
        console.log('5. 在左侧找到 Cookies → https://x.com');
        console.log('6. 找到名为 "auth_token" 的cookie');
        console.log('7. 复制其值（通常是40个字符的字符串）');
        console.log('8. 将该值输入到下面的提示中\n');
        console.log('注意: auth_token通常看起来像这样: a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0');
        console.log('');
    }

    /**
     * 测试OAuth流程
     */
    async testOAuthWithValidToken() {
        console.log('🚀 测试使用有效Twitter Token的OAuth流程\n');
        
        try {
            // 显示获取token的说明
            this.showTokenInstructions();
            
            // 获取用户输入的Twitter auth token
            const authToken = await this.getUserInput('请输入您的Twitter auth_token: ');
            
            if (!authToken || authToken.length < 20) {
                throw new Error('无效的auth_token，请确保输入正确的token');
            }
            
            console.log(`\n✅ 收到auth_token: ${authToken.substring(0, 10)}...`);
            
            // 创建Twitter OAuth实例
            this.twitter = new TwitterOAuth(authToken);
            
            // 获取测试用的OAuth token
            const oauthToken = await this.getUserInput('\n请输入OAuth token (从Sosovalue获取): ');
            
            if (!oauthToken) {
                throw new Error('OAuth token不能为空');
            }
            
            console.log(`\n✅ 收到OAuth token: ${oauthToken}`);
            
            // 测试获取Twitter tokens
            console.log('\n=== 步骤1: 获取Twitter Tokens ===');
            const tokensResult = await this.twitter.getTwitterTokens(oauthToken);
            
            if (!tokensResult) {
                throw new Error('获取Twitter tokens失败');
            }
            
            console.log('✅ Twitter tokens获取成功');
            const state = this.twitter.getState();
            console.log(`   Authenticity Token: ${state.authenticityToken?.substring(0, 20)}...`);
            console.log(`   CSRF Token: ${state.csrfToken?.substring(0, 20)}...`);
            
            // 测试OAuth授权
            console.log('\n=== 步骤2: 执行OAuth授权 ===');
            const authResult = await this.twitter.authorizeWithTwitter(oauthToken);
            
            if (authResult.success) {
                console.log('🎉 OAuth授权成功!');
                console.log(`   OAuth Verifier: ${authResult.oauthVerifier}`);
                
                console.log('\n' + '='.repeat(60));
                console.log('🏆 Twitter OAuth流程完全成功!');
                console.log('='.repeat(60));
                console.log('✅ Twitter tokens获取成功');
                console.log('✅ OAuth授权成功');
                console.log('✅ OAuth verifier获取成功');
                console.log('\n🎯 现在可以使用这个oauth_verifier完成Sosovalue绑定!');
                
                return {
                    success: true,
                    oauthToken: oauthToken,
                    oauthVerifier: authResult.oauthVerifier
                };
                
            } else {
                console.log('❌ OAuth授权失败');
                console.log(`   错误: ${authResult.error}`);
                
                // 分析失败原因
                console.log('\n🔍 失败原因分析:');
                if (authResult.error.includes('login/error')) {
                    console.log('   - Twitter auth token可能已过期或无效');
                    console.log('   - 请重新获取auth_token');
                } else if (authResult.error.includes('404')) {
                    console.log('   - OAuth token可能无效或已过期');
                    console.log('   - 请从Sosovalue重新获取OAuth token');
                } else {
                    console.log(`   - 其他错误: ${authResult.error}`);
                }
                
                return { success: false, error: authResult.error };
            }
            
        } catch (error) {
            console.log('\n' + '='.repeat(60));
            console.log('❌ 测试失败!');
            console.log('='.repeat(60));
            console.error(`错误: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 演示完整的Sosovalue绑定流程
     */
    async demonstrateFullFlow() {
        console.log('=== 完整Sosovalue Twitter绑定流程演示 ===\n');
        
        console.log('📋 完整流程步骤:');
        console.log('1. 获取有效的Twitter auth_token (从x.com)');
        console.log('2. 登录Sosovalue平台');
        console.log('3. 获取Twitter OAuth授权URL和oauth_token');
        console.log('4. 使用我们的TwitterOAuth类自动完成授权');
        console.log('5. 获取oauth_verifier');
        console.log('6. 调用Sosovalue绑定API完成绑定');
        console.log('');
        
        console.log('🔧 技术实现:');
        console.log('- TwitterOAuth类已简化并修复');
        console.log('- 支持自动获取authenticity_token和CSRF token');
        console.log('- 支持自动处理OAuth授权流程');
        console.log('- 提供详细的错误诊断和调试信息');
        console.log('');
        
        console.log('✅ 已验证的功能:');
        console.log('- ✅ 钱包初始化和登录');
        console.log('- ✅ Sosovalue API集成');
        console.log('- ✅ Twitter OAuth URL获取');
        console.log('- ✅ Twitter tokens获取');
        console.log('- ✅ OAuth授权流程');
        console.log('- ✅ 错误处理和调试');
        console.log('');
        
        console.log('🎯 使用方法:');
        console.log('1. 确保有有效的Twitter auth_token');
        console.log('2. 运行: node sosovalue-twitter-bind.js');
        console.log('3. 系统将自动完成整个绑定流程');
        console.log('');
        
        const continueTest = await this.getUserInput('是否要测试OAuth流程? (y/n): ');
        if (continueTest.toLowerCase() === 'y') {
            await this.testOAuthWithValidToken();
        }
    }
}

// 主程序
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args[0] === 'demo') {
        const test = new ValidTokenTest();
        test.demonstrateFullFlow();
    } else {
        const test = new ValidTokenTest();
        test.testOAuthWithValidToken();
    }
}

module.exports = ValidTokenTest;
