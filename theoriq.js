const theoriqManager = require('./TheoriqManager')
const logger = require('./utils/logger')

/**
 * 解析命令行参数
 */
function parseWalletIndexes(args) {
  let walletIndexes = []
  
  for (let i = 0; i < args.length; i++) {
    if (args[i] === '-i' && i + 1 < args.length) {
      const indexArg = args[i + 1]
      
      if (indexArg.includes(',')) {
        // 格式: 1,3,5
        walletIndexes = indexArg.split(',').map(idx => parseInt(idx.trim()))
      } else if (indexArg.includes('-')) {
        // 格式: 1-5
        const [start, end] = indexArg.split('-').map(idx => parseInt(idx.trim()))
        for (let j = start; j <= end; j++) {
          walletIndexes.push(j)
        }
      } else {
        // 单个索引
        walletIndexes = [parseInt(indexArg.trim())]
      }
      break
    } else if (!isNaN(parseInt(args[i]))) {
      // 直接的数字参数
      walletIndexes.push(parseInt(args[i]))
    }
  }
  
  return walletIndexes
}

async function main() {
  try {
    const args = process.argv.slice(2)
    
    if (args[0] === 'login') {
      // 登录命令
      const walletIndexes = parseWalletIndexes(args.slice(1))
      
      if (walletIndexes.length > 0) {
        console.log(`登录指定钱包: ${walletIndexes.join(', ')}`)
        const result = await theoriqManager.loginAllWallets(walletIndexes)
        
        console.log('\n=== 登录结果 ===')
        console.log(`总计: ${result.total}`)
        console.log(`成功: ${result.success}`)
        console.log(`失败: ${result.failed}`)
        console.log(`缓存: ${result.fromCache}`)
      } else {
        console.log('登录所有钱包')
        const result = await theoriqManager.loginAllWallets()
        
        console.log('\n=== 登录结果 ===')
        console.log(`总计: ${result.total}`)
        console.log(`成功: ${result.success}`)
        console.log(`失败: ${result.failed}`)
        console.log(`缓存: ${result.fromCache}`)
      }
      
    } else if (args[0] === 'stats') {
      // 统计信息
      const walletIndex = args[1] ? parseInt(args[1]) : null
      theoriqManager.showStats(walletIndex)
      
    } else if (args[0] === 'config') {
      // 配置信息
      const config = theoriqManager.config
      console.log('\n=== 配置信息 ===')
      console.log(`API地址: ${config.theoriq.baseUrl}`)
      console.log(`代理: ${config.proxy.enabled ? config.proxy.url : '未启用'}`)
      console.log(`区块链: ${config.theoriq.blockchainName}`)
      console.log(`重试次数: ${config.maxRetries}`)
      console.log(`请求超时: ${config.requestTimeout}ms`)
      
    } else if (args[0] === 'help' || args[0] === '-h') {
      // 帮助信息
      console.log('\n=== Theoriq Manager 使用说明 ===')
      console.log('命令:')
      console.log('  node theoriq.js login              - 登录所有钱包')
      console.log('  node theoriq.js login 1 2 3        - 登录指定钱包')
      console.log('  node theoriq.js login -i 1-5       - 登录钱包1到5')
      console.log('  node theoriq.js login -i 1,3,5     - 登录钱包1,3,5')
      console.log('  node theoriq.js stats              - 查看所有钱包状态')
      console.log('  node theoriq.js stats 1            - 查看钱包1状态')
      console.log('  node theoriq.js config             - 查看配置')
      console.log('  node theoriq.js help               - 显示帮助')
      
    } else {
      // 默认显示状态
      console.log('\n=== Theoriq Manager ===')
      const stats = theoriqManager.getStats()
      console.log(`钱包总数: ${stats.total}`)
      console.log(`已登录: ${stats.loginCount}`)
      console.log(`未登录: ${stats.total - stats.loginCount}`)
      
      if (stats.loginCount === 0) {
        console.log('\n💡 使用 "node theoriq.js login" 登录钱包')
      }
      
      console.log('\n使用 "node theoriq.js help" 查看完整命令')
    }
    
  } catch (error) {
    logger.error('程序执行失败', error.message)
    console.error('错误:', error.message)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}

module.exports = { theoriqManager } 