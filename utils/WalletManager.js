const fs = require('fs')
const path = require('path')

class WalletManager {
  constructor() {
    this.wallets = new Map()
    this.isLoaded = false
    this.loadWallets()
  }

  loadWallets() {
    if (this.isLoaded) {
      return
    }

    try {
      // 同步读取 CSV 文件
      const csvPath = path.join(__dirname, '../wallets.csv')
      const content = fs.readFileSync(csvPath, 'utf-8')
      
      // 按行分割并移除空行
      const lines = content.split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0)
      
      // 验证表头
      const header = lines[0]
      if (header !== 'index,addr,pk') {
        throw new Error('CSV 文件格式错误：需要 index,addr,pk 表头')
      }
      
      // 处理数据行
      lines.slice(1).forEach(line => {
        const [index, addr, pk] = line.split(',').map(item => item.trim())
        
        // 验证数据完整性
        if (!index || !addr || !pk) {
          console.warn('跳过无效行:', line)
          return
        }
        
        const walletIndex = parseInt(index)
        if (isNaN(walletIndex) || walletIndex < 1) {
          console.warn('无效的钱包索引:', index)
          return
        }
        
        this.wallets.set(walletIndex, {
          index: walletIndex,
          addr: addr,
          pk: pk
        })
      })
      
      this.isLoaded = true
      console.log(`成功加载 ${this.wallets.size} 个钱包`)
    } catch (error) {
      console.error('加载钱包失败:', error)
      throw error
    }
  }

  getWallet(index) {
    const wallet = this.wallets.get(parseInt(index))
    if (!wallet) {
      throw new Error(`找不到索引为 ${index} 的钱包`)
    }
    return wallet
  }

  getAllWallets() {
    return Array.from(this.wallets.values())
  }

  getWalletCount() {
    return this.wallets.size
  }

  hasWallet(index) {
    return this.wallets.has(parseInt(index))
  }
}

// 创建单例实例
const walletManager = new WalletManager()

module.exports = walletManager 