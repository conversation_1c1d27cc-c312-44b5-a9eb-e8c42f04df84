const { 
  green, red, blue, yellow, cyan, gray, magenta 
} = require('colorette')
const fs = require('fs')
const path = require('path')

class Logger {
  constructor() {
    // 创建日志目录
    this.logDir = path.join(process.cwd(), 'logs')
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true })
    }

    // 当前日志文件 - 简单的文本日志
    const date = new Date().toISOString().split('T')[0]
    this.logFile = path.join(this.logDir, `${date}.log`)
  }

  /**
   * 写入日志文件
   */
  writeToFile(level, message) {
    const timestamp = new Date().toISOString()
    const logEntry = `[${timestamp}] [${level}] ${message}\n`
    fs.appendFileSync(this.logFile, logEntry)
  }

  /**
   * 格式化对象数据用于控制台显示
   */
  formatObject(obj) {
    if (!obj) return ''
    if (typeof obj !== 'object') return ` ${obj}`

    // 将对象转换为单行文本
    return ' ' + Object.entries(obj)
      .map(([key, value]) => `${key}: ${value}`)
      .join(', ')
  }

  /**
   * 成功日志
   */
  success(message, data = '') {
    const formattedData = this.formatObject(data)
    console.log(green('✓ ') + green(message) + gray(formattedData))
    this.writeToFile('SUCCESS', message + formattedData)
  }

  /**
   * 错误日志
   */
  error(message, error = '') {
    const formattedError = this.formatObject(error)
    console.error(red('✗ ') + red(message) + gray(formattedError))
    this.writeToFile('ERROR', message + formattedError)
  }

  /**
   * 信息日志
   */
  info(message, data = '') {
    const formattedData = this.formatObject(data)
    console.log(blue('ℹ ') + message + gray(formattedData))
    this.writeToFile('INFO', message + formattedData)
  }

  /**
   * 警告日志
   */
  warn(message, data = '') {
    const formattedData = this.formatObject(data)
    console.log(yellow('⚠ ') + yellow(message) + gray(formattedData))
    this.writeToFile('WARN', message + formattedData)
  }

  /**
   * 任务开始
   */
  taskStart(taskName) {
    console.log('\n' + cyan('▶ ') + cyan(`开始任务: ${taskName}`))
    this.writeToFile('TASK', `开始任务: ${taskName}`)
  }

  /**
   * 任务完成
   */
  taskEnd(taskName, stats = {}) {
    console.log(cyan('◼ ') + cyan(`任务完成: ${taskName}`))
    if (stats && Object.keys(stats).length > 0) {
      console.log(gray('统计信息:'))
      Object.entries(stats).forEach(([key, value]) => {
        console.log(gray(`  ${key}: ${value}`))
      })
    }
    this.writeToFile('TASK', `任务完成: ${taskName}`)
  }

  /**
   * 钱包操作日志
   */
  wallet(index, message, data = '', isError = false) {
    const formattedData = this.formatObject(data)
    const messageColor = isError ? red : (text => text)  // 如果是错误则使用红色
    console.log(
      magenta(`[钱包 ${index}] `) + 
      messageColor(message) + 
      gray(formattedData)
    )
    this.writeToFile('WALLET', `[钱包 ${index}] ${message}${formattedData}`)
  }

  /**
   * 交易日志 - 专门用于记录交易相关的日志
   */
  transaction(walletIndex, taskName, message, status, data = {}, txHash = '') {
    const formattedData = this.formatObject(data)
    const statusIcon = status === 'success' ? green('✓') : status === 'failed' ? red('✗') : yellow('⏳')
    
    console.log(
      magenta(`[钱包 ${walletIndex}] `) + 
      statusIcon + ' ' + message + 
      gray(formattedData)
    )
    this.writeToFile('TRANSACTION', `[钱包 ${walletIndex}] ${message}${formattedData}`)
  }
}

// 创建单例
const logger = new Logger()
module.exports = logger 