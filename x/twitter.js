const axios = require('axios')
const { HttpsProxyAgent } = require('https-proxy-agent')
const logger = require('../utils/logger')

/**
 * 通用的Twitter OAuth 1.0授权类
 * 支持完整的OAuth 1.0流程：request token → 用户授权 → access token
 */
class TwitterOAuth {
  constructor(authToken, proxyUrl = null) {
    this.authToken = authToken
    this.bearerToken = "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA"

    // OAuth状态管理
    this.state = {
      authenticityToken: null,
      oauthVerifier: null,
      csrfToken: null,
      requestToken: null,
      requestTokenSecret: null,
      accessToken: null,
      accessTokenSecret: null
    }

    // 默认请求头
    this.defaultHeaders = {
      "authority": "api.x.com",
      "origin": "https://x.com",
      "x-twitter-active-user": "yes",
      "x-twitter-client-language": "en",
      "authorization": this.bearerToken,
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
      "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
      "accept-language": "en-US,en;q=0.5",
      "accept-encoding": "gzip, deflate, br",
      "referer": "https://x.com/"
    }

    // 默认cookies
    this.defaultCookies = `auth_token=${authToken}`

    // axios配置
    this.axiosConfig = {
      timeout: 120000,
      headers: {
        ...this.defaultHeaders,
        "cookie": this.defaultCookies
      },
      maxRedirects: 5,
      validateStatus: status => status >= 200 && status < 500
    }

    // 代理配置
    if (proxyUrl) {
      this.axiosConfig.httpsAgent = new HttpsProxyAgent(proxyUrl)
    }
  }

  /**
   * 重置OAuth状态
   */
  resetState() {
    this.state = {
      authenticityToken: null,
      oauthVerifier: null,
      csrfToken: null,
      requestToken: null,
      requestTokenSecret: null,
      accessToken: null,
      accessTokenSecret: null
    }
  }

  /**
   * 获取当前OAuth状态
   */
  getState() {
    return { ...this.state }
  }

  /**
   * 获取Twitter认证token和CSRF token
   * @param {string} oauthToken OAuth token
   * @returns {Promise<boolean>} 是否成功获取tokens
   */
  async getTwitterTokens(oauthToken) {
    try {
      logger.info('获取Twitter tokens', { oauthToken })

      const response = await axios.get(
        `https://api.x.com/oauth/authorize?oauth_token=${oauthToken}`,
        this.axiosConfig
      )

      // 提取CSRF token从cookies
      if (response.headers['set-cookie']) {
        const cookies = response.headers['set-cookie']
        for (const cookie of cookies) {
          if (cookie.includes('ct0=')) {
            const csrfMatch = cookie.match(/ct0=([^;]+)/)
            if (csrfMatch && csrfMatch[1]) {
              this.state.csrfToken = csrfMatch[1]
              logger.info('获取CSRF token成功', {
                token: this.state.csrfToken.substring(0, 10) + '...'
              })
              break
            }
          }
        }
      }

      if (response.data && response.data.includes('authenticity_token')) {
        // 解析authenticity_token
        const tokenMatch = response.data.match(/authenticity_token" value="([^"]+)"/);
        if (tokenMatch && tokenMatch[1]) {
          this.state.authenticityToken = tokenMatch[1]
          logger.success('获取authenticity_token成功', {
            token: this.state.authenticityToken.substring(0, 10) + '...'
          })
          return true
        }
      }

      logger.error('获取authenticity_token失败 - 未找到token')
      return false

    } catch (error) {
      logger.error('获取authenticity_token失败', error.message)
      return false
    }
  }

  /**
   * 兼容性方法 - 保持与现有代码的兼容性
   */
  async getTwitterToken(oauthToken) {
    return await this.getTwitterTokens(oauthToken)
  }

  /**
   * 执行Twitter OAuth授权
   * @param {string} oauthToken OAuth token
   * @returns {Promise<Object>} 授权结果
   */
  async performOAuthAuthorization(oauthToken) {
    try {
      // 先获取必要的tokens
      if (!await this.getTwitterTokens(oauthToken)) {
        return {
          success: false,
          error: '获取Twitter tokens失败'
        }
      }

      logger.info('执行Twitter OAuth授权', { oauthToken })

      // 尝试多种授权方法
      const authMethods = [
        {
          name: '标准OAuth授权',
          url: 'https://api.x.com/oauth/authorize',
          data: new URLSearchParams({
            'authenticity_token': this.state.authenticityToken,
            'redirect_after_login': `https://api.x.com/oauth/authorize?oauth_token=${oauthToken}`,
            'oauth_token': oauthToken
          })
        },
        {
          name: '带授权参数的OAuth',
          url: 'https://api.x.com/oauth/authorize',
          data: new URLSearchParams({
            'authenticity_token': this.state.authenticityToken,
            'redirect_after_login': `https://api.x.com/oauth/authorize?oauth_token=${oauthToken}`,
            'oauth_token': oauthToken,
            'authorize': '1'
          })
        }
      ]

      for (const method of authMethods) {
        logger.info(`尝试${method.name}`)

        // 准备请求头
        const headers = {
          ...this.axiosConfig.headers,
          'content-type': 'application/x-www-form-urlencoded',
          'referer': `https://api.x.com/oauth/authorize?oauth_token=${oauthToken}`,
          'origin': 'https://api.x.com'
        }

        // 添加CSRF token
        if (this.state.csrfToken) {
          headers['x-csrf-token'] = this.state.csrfToken
          headers['cookie'] = `${this.defaultCookies}; ct0=${this.state.csrfToken}`
        }

        try {
          const response = await axios.post(method.url, method.data, {
            ...this.axiosConfig,
            headers: headers
          })

          const result = this.extractOAuthVerifier(response)
          if (result.success) {
            logger.success(`${method.name}成功`, {
              verifier: result.oauthVerifier.substring(0, 10) + '...'
            })
            return result
          }
        } catch (error) {
          logger.warn(`${method.name}失败: ${error.message}`)
          continue
        }
      }

      return {
        success: false,
        error: '所有授权方法都失败了'
      }

    } catch (error) {
      logger.error('Twitter OAuth授权失败', error.message)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 从响应中提取OAuth verifier
   * @param {Object} response HTTP响应对象
   * @returns {Object} 提取结果
   */
  extractOAuthVerifier(response) {
    try {
      const responseText = typeof response.data === 'string' ? response.data : JSON.stringify(response.data)

      if (responseText && responseText.includes('oauth_verifier')) {
        // 解析oauth_verifier
        const verifierMatch = responseText.match(/oauth_verifier=([^"&]+)/);
        if (verifierMatch && verifierMatch[1]) {
          this.state.oauthVerifier = verifierMatch[1]
          return {
            success: true,
            oauthVerifier: this.state.oauthVerifier
          }
        }
      }

      return {
        success: false,
        error: '响应中未找到oauth_verifier'
      }

    } catch (error) {
      return {
        success: false,
        error: `解析响应失败: ${error.message}`
      }
    }
  }

  /**
   * 兼容性方法 - Twitter OAuth授权
   * @param {string} oauthToken OAuth token
   * @returns {Promise<boolean>} 是否成功
   */
  async twitterAuthorize(oauthToken) {
    const result = await this.performOAuthAuthorization(oauthToken)
    return result.success
  }

  /**
   * 处理Twitter OAuth回调
   * 模拟访问Theoriq的回调URL来完成绑定
   * @param {string} oauthToken OAuth token
   * @param {string} oauthVerifier OAuth verifier
   * @returns {Object} 回调处理结果
   */
  async handleOAuthCallback(oauthToken, oauthVerifier) {
    try {
      logger.info('处理Twitter OAuth回调', { oauthToken, verifier: oauthVerifier?.substring(0, 10) + '...' })
      
      // 构建回调URL
      const callbackUrl = `https://api-theoriq.bonusblock.io/oauth/callback/twitter?client=theoriq&returnTo=https%3A%2F%2Fquests.theoriq.ai%2Fquests&oauth_token=${oauthToken}&oauth_verifier=${oauthVerifier}`
      
      // 发送回调请求
      const response = await axios.get(callbackUrl, {
        ...this.axiosConfig,
        maxRedirects: 10, // 允许更多重定向
        validateStatus: status => status >= 200 && status < 400
      })
      
      logger.success('Twitter OAuth回调处理成功', {
        status: response.status,
        redirected: response.request?.res?.responseUrl || 'N/A'
      })
      
      return {
        success: true,
        status: response.status,
        data: response.data,
        finalUrl: response.request?.res?.responseUrl
      }
      
    } catch (error) {
      logger.error('Twitter OAuth回调处理失败', error.message)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 获取OAuth verifier
   * @returns {string|null} OAuth verifier
   */
  getOauthVerifier() {
    return this.state.oauthVerifier
  }

  /**
   * 获取authenticity token
   * @returns {string|null} Authenticity token
   */
  getAuthenticityToken() {
    return this.state.authenticityToken
  }

  /**
   * 重置tokens
   */
  resetTokens() {
    this.resetState()
  }

  /**
   * 完整的Twitter OAuth流程（包含回调处理）
   * @param {string} oauthToken OAuth token
   * @param {boolean} handleCallback 是否处理回调（默认true）
   * @returns {Object} 包含成功状态和相关数据的对象
   */
  async completeOAuthFlow(oauthToken, handleCallback = true) {
    try {
      logger.info('开始Twitter OAuth完整流程', { oauthToken, handleCallback })
      
      // 1. 执行Twitter授权
      const success = await this.twitterAuthorize(oauthToken)
      
      if (!success) {
        return {
          success: false,
          error: 'OAuth授权失败'
        }
      }
      
      const result = {
        success: true,
        oauthVerifier: this.state.oauthVerifier,
        authenticityToken: this.state.authenticityToken
      }

      // 2. 如果需要，处理回调
      if (handleCallback) {
        const callbackResult = await this.handleOAuthCallback(oauthToken, this.state.oauthVerifier)
        result.callback = callbackResult
        
        if (!callbackResult.success) {
          logger.warn('回调处理失败，但OAuth流程已完成', callbackResult.error)
        }
      }
      
      return result
      
    } catch (error) {
      logger.error('Twitter OAuth流程失败', error.message)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 仅执行OAuth流程，不处理回调
   * @param {string} oauthToken OAuth token
   * @returns {Object} OAuth结果
   */
  async oauthOnly(oauthToken) {
    return await this.completeOAuthFlow(oauthToken, false)
  }

  /**
   * 获取OAuth verifier - 新的公共接口
   * @param {string} oauthToken OAuth token
   * @returns {Promise<Object>} 包含oauth_verifier的结果
   */
  async getOAuthVerifier(oauthToken) {
    const result = await this.performOAuthAuthorization(oauthToken)
    if (result.success) {
      return {
        success: true,
        oauthVerifier: this.state.oauthVerifier,
        oauthToken: oauthToken,
        authenticityToken: this.state.authenticityToken
      }
    } else {
      return result
    }
  }

  /**
   * 重置tokens - 兼容性方法
   */
  resetTokens() {
    this.resetState()
  }

  /**
   * 获取authenticity_token - 兼容性方法
   */
  getAuthenticityToken() {
    return this.state.authenticityToken
  }

  /**
   * 获取oauth_verifier - 兼容性方法
   */
  getOauthVerifier() {
    return this.state.oauthVerifier
  }
}

// 为了兼容性，同时导出两个类名
class Twitter extends TwitterOAuth {}

module.exports = TwitterOAuth
module.exports.Twitter = Twitter
module.exports.TwitterOAuth = TwitterOAuth
