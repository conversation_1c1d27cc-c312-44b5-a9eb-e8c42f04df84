const axios = require('axios')
const { HttpsProxyAgent } = require('https-proxy-agent')
const logger = require('../utils/logger')

class Twitter {
  constructor(authToken, proxyUrl = null) {
    this.authToken = authToken
    this.bearerToken = "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA"
    
    // 默认请求头
    this.defaultHeaders = {
      "authority": "twitter.com",
      "origin": "https://x.com",
      "x-twitter-active-user": "yes",
      "x-twitter-client-language": "en",
      "authorization": this.bearerToken,
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
      "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
      "accept-language": "en-US,en;q=0.5",
      "accept-encoding": "gzip, deflate, br",
      "referer": "https://x.com/"
    }
    
    // 默认cookies
    this.defaultCookies = `auth_token=${authToken}`
    
    // axios配置
    this.axiosConfig = {
      timeout: 120000,
      headers: {
        ...this.defaultHeaders,
        "cookie": this.defaultCookies
      },
      maxRedirects: 5,
      validateStatus: status => status >= 200 && status < 500
    }
    
    // 代理配置
    if (proxyUrl) {
      this.axiosConfig.httpsAgent = new HttpsProxyAgent(proxyUrl)
    }
    
    this.authenticityToken = null
    this.oauthVerifier = null
  }

  /**
   * 获取Twitter认证token
   * @param {string} oauthToken OAuth token
   * @returns {boolean} 是否成功获取authenticity_token
   */
  async getTwitterToken(oauthToken) {
    try {
      logger.info('获取Twitter authenticity_token', { oauthToken })
      
      const response = await axios.get(
        `https://api.x.com/oauth/authorize?oauth_token=${oauthToken}`,
        this.axiosConfig
      )
      
      if (response.data && response.data.includes('authenticity_token')) {
        // 解析authenticity_token
        const tokenMatch = response.data.match(/authenticity_token" value="([^"]+)"/);
        if (tokenMatch && tokenMatch[1]) {
          this.authenticityToken = tokenMatch[1]
          logger.success('获取authenticity_token成功', { 
            token: this.authenticityToken.substring(0, 10) + '...' 
          })
          return true
        }
      }
      
      logger.error('获取authenticity_token失败 - 未找到token')
      return false
      
    } catch (error) {
      logger.error('获取authenticity_token失败', error.message)
      return false
    }
  }

  /**
   * Twitter OAuth授权
   * @param {string} oauthToken OAuth token
   * @returns {boolean} 是否成功获取oauth_verifier
   */
  async twitterAuthorize(oauthToken) {
    try {
      // 先获取authenticity_token
      if (!await this.getTwitterToken(oauthToken)) {
        return false
      }
      
      logger.info('执行Twitter OAuth授权', { oauthToken })
      
      // 准备POST数据
      const postData = new URLSearchParams({
        'authenticity_token': this.authenticityToken,
        'redirect_after_login': `https://api.x.com/oauth/authorize?oauth_token=${oauthToken}`,
        'oauth_token': oauthToken
      })
      
      // 发送授权请求
      const response = await axios.post(
        'https://api.x.com/oauth/authorize',
        postData,
        {
          ...this.axiosConfig,
          headers: {
            ...this.axiosConfig.headers,
            'content-type': 'application/x-www-form-urlencoded'
          }
        }
      )
      
      const responseText = typeof response.data === 'string' ? response.data : JSON.stringify(response.data)

      if (responseText && responseText.includes('oauth_verifier')) {
        // 解析oauth_verifier
        const verifierMatch = responseText.match(/oauth_verifier=([^"&]+)/);
        if (verifierMatch && verifierMatch[1]) {
          this.oauthVerifier = verifierMatch[1]
          logger.success('Twitter OAuth授权成功', {
            verifier: this.oauthVerifier.substring(0, 10) + '...'
          })
          return true
        }
      }
      
      logger.error('Twitter OAuth授权失败 - 未找到oauth_verifier')
      return false
      
    } catch (error) {
      logger.error('Twitter OAuth授权失败', error.message)
      return false
    }
  }

  /**
   * 处理Twitter OAuth回调
   * 模拟访问Theoriq的回调URL来完成绑定
   * @param {string} oauthToken OAuth token
   * @param {string} oauthVerifier OAuth verifier
   * @returns {Object} 回调处理结果
   */
  async handleOAuthCallback(oauthToken, oauthVerifier) {
    try {
      logger.info('处理Twitter OAuth回调', { oauthToken, verifier: oauthVerifier?.substring(0, 10) + '...' })
      
      // 构建回调URL
      const callbackUrl = `https://api-theoriq.bonusblock.io/oauth/callback/twitter?client=theoriq&returnTo=https%3A%2F%2Fquests.theoriq.ai%2Fquests&oauth_token=${oauthToken}&oauth_verifier=${oauthVerifier}`
      
      // 发送回调请求
      const response = await axios.get(callbackUrl, {
        ...this.axiosConfig,
        maxRedirects: 10, // 允许更多重定向
        validateStatus: status => status >= 200 && status < 400
      })
      
      logger.success('Twitter OAuth回调处理成功', {
        status: response.status,
        redirected: response.request?.res?.responseUrl || 'N/A'
      })
      
      return {
        success: true,
        status: response.status,
        data: response.data,
        finalUrl: response.request?.res?.responseUrl
      }
      
    } catch (error) {
      logger.error('Twitter OAuth回调处理失败', error.message)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 获取OAuth verifier
   * @returns {string|null} OAuth verifier
   */
  getOauthVerifier() {
    return this.oauthVerifier
  }

  /**
   * 获取authenticity token
   * @returns {string|null} Authenticity token
   */
  getAuthenticityToken() {
    return this.authenticityToken
  }

  /**
   * 重置tokens
   */
  resetTokens() {
    this.authenticityToken = null
    this.oauthVerifier = null
  }

  /**
   * 完整的Twitter OAuth流程（包含回调处理）
   * @param {string} oauthToken OAuth token
   * @param {boolean} handleCallback 是否处理回调（默认true）
   * @returns {Object} 包含成功状态和相关数据的对象
   */
  async completeOAuthFlow(oauthToken, handleCallback = true) {
    try {
      logger.info('开始Twitter OAuth完整流程', { oauthToken, handleCallback })
      
      // 1. 执行Twitter授权
      const success = await this.twitterAuthorize(oauthToken)
      
      if (!success) {
        return {
          success: false,
          error: 'OAuth授权失败'
        }
      }
      
      const result = {
        success: true,
        oauthVerifier: this.oauthVerifier,
        authenticityToken: this.authenticityToken
      }
      
      // 2. 如果需要，处理回调
      if (handleCallback) {
        const callbackResult = await this.handleOAuthCallback(oauthToken, this.oauthVerifier)
        result.callback = callbackResult
        
        if (!callbackResult.success) {
          logger.warn('回调处理失败，但OAuth流程已完成', callbackResult.error)
        }
      }
      
      return result
      
    } catch (error) {
      logger.error('Twitter OAuth流程失败', error.message)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 仅执行OAuth流程，不处理回调
   * @param {string} oauthToken OAuth token
   * @returns {Object} OAuth结果
   */
  async oauthOnly(oauthToken) {
    return await this.completeOAuthFlow(oauthToken, false)
  }
}

module.exports = Twitter
