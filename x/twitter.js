const axios = require('axios')
const { HttpsProxyAgent } = require('https-proxy-agent')
const logger = require('../utils/logger')

class TwitterOAuth {
  constructor(authToken, proxyUrl = null) {
    this.authToken = authToken

    // 简化的状态管理
    this.state = {
      authenticityToken: null,
      oauthVerifier: null,
      csrfToken: null
    }

    // 默认请求头
    this.defaultHeaders = {
      "authority": "api.x.com",
      "origin": "https://x.com",
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
      "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
      "accept-language": "en-US,en;q=0.5",
      "accept-encoding": "gzip, deflate, br",
      "referer": "https://x.com/"
    }

    // 默认cookies
    this.defaultCookies = `auth_token=${authToken}`

    // axios配置
    this.axiosConfig = {
      timeout: 30000,
      headers: {
        ...this.defaultHeaders,
        "cookie": this.defaultCookies
      },
      maxRedirects: 0, // 不自动跟随重定向
      validateStatus: status => status >= 200 && status < 400
    }

    // 代理配置
    if (proxyUrl) {
      this.axiosConfig.httpsAgent = new HttpsProxyAgent(proxyUrl)
    }
  }

  /**
   * 重置OAuth状态
   */
  resetState() {
    this.state = {
      authenticityToken: null,
      oauthVerifier: null,
      csrfToken: null,
      requestToken: null,
      requestTokenSecret: null,
      accessToken: null,
      accessTokenSecret: null
    }
  }

  /**
   * 获取当前OAuth状态
   */
  getState() {
    return { ...this.state }
  }

  /**
   * 获取Twitter认证tokens
   * @param {string} oauthToken OAuth token
   * @returns {Promise<boolean>} 是否成功获取tokens
   */
  async getTwitterTokens(oauthToken) {
    try {
      logger.info('获取Twitter授权页面', { oauthToken })

      const response = await axios.get(
        `https://api.x.com/oauth/authorize?oauth_token=${oauthToken}`,
        this.axiosConfig
      )

      // 提取CSRF token从cookies
      if (response.headers['set-cookie']) {
        const cookies = response.headers['set-cookie']
        for (const cookie of cookies) {
          if (cookie.includes('ct0=')) {
            const csrfMatch = cookie.match(/ct0=([^;]+)/)
            if (csrfMatch && csrfMatch[1]) {
              this.state.csrfToken = csrfMatch[1]
              logger.info('获取CSRF token成功')
              break
            }
          }
        }
      }

      // 提取authenticity_token
      if (response.data && response.data.includes('authenticity_token')) {
        const tokenMatch = response.data.match(/authenticity_token" value="([^"]+)"/);
        if (tokenMatch && tokenMatch[1]) {
          this.state.authenticityToken = tokenMatch[1]
          logger.success('获取authenticity_token成功')
          return true
        }
      }

      logger.error('未找到authenticity_token')
      return false

    } catch (error) {
      logger.error('获取Twitter tokens失败', error.message)
      return false
    }
  }

  /**
   * 兼容性方法 - 保持与现有代码的兼容性
   */
  async getTwitterToken(oauthToken) {
    return await this.getTwitterTokens(oauthToken)
  }

  /**
   * 执行Twitter OAuth授权
   * @param {string} oauthToken OAuth token
   * @returns {Promise<Object>} 授权结果
   */
  async authorizeWithTwitter(oauthToken) {
    try {
      // 先获取必要的tokens
      if (!await this.getTwitterTokens(oauthToken)) {
        return {
          success: false,
          error: '获取Twitter tokens失败'
        }
      }

      logger.info('开始Twitter OAuth授权', { oauthToken })

      // Twitter OAuth 1.0a 流程：
      // 1. 用户访问授权页面 (已完成)
      // 2. 用户点击"授权"按钮，这会POST到授权端点
      // 3. Twitter重定向到回调URL，包含oauth_verifier

      // 准备授权请求 - 根据实际观察到的流程
      const postData = new URLSearchParams({
        'authenticity_token': this.state.authenticityToken,
        'oauth_token': oauthToken
      })

      // 准备请求头 - 根据实际观察到的请求头
      const headers = {
        'content-type': 'application/x-www-form-urlencoded',
        'origin': 'https://api.x.com',
        'referer': 'https://api.x.com/',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'sec-fetch-site': 'same-site',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-user': '?1',
        'sec-fetch-dest': 'document',
        'upgrade-insecure-requests': '1',
        'cache-control': 'max-age=0'
      }

      // 添加cookies - 包含auth_token和ct0
      if (this.state.csrfToken) {
        headers['cookie'] = `${this.defaultCookies}; ct0=${this.state.csrfToken}`
      } else {
        headers['cookie'] = this.defaultCookies
      }

      try {
        // 发送授权请求到正确的URL
        const response = await axios.post(
          'https://x.com/oauth/authorize',
          postData,
          {
            timeout: 30000,
            headers: headers,
            maxRedirects: 5, // 允许重定向来获取verifier
            validateStatus: status => status >= 200 && status < 400
          }
        )

        // 从响应中提取oauth_verifier
        const verifier = this.extractVerifierFromResponse(response)
        if (verifier) {
          this.state.oauthVerifier = verifier
          logger.success('Twitter OAuth授权成功', {
            verifier: verifier.substring(0, 10) + '...'
          })
          return {
            success: true,
            oauthVerifier: verifier
          }
        }

      } catch (error) {
        // 如果是重定向错误，检查Location header
        if (error.response && error.response.status >= 300 && error.response.status < 400) {
          const verifier = this.extractVerifierFromResponse(error.response)
          if (verifier) {
            this.state.oauthVerifier = verifier
            logger.success('Twitter OAuth授权成功 (通过重定向)', {
              verifier: verifier.substring(0, 10) + '...'
            })
            return {
              success: true,
              oauthVerifier: verifier
            }
          }
        }
        throw error
      }

      return {
        success: false,
        error: '未能获取oauth_verifier'
      }

    } catch (error) {
      logger.error('Twitter OAuth授权失败', error.message)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 从响应中提取OAuth verifier
   * @param {Object} response HTTP响应对象
   * @returns {string|null} OAuth verifier或null
   */
  extractVerifierFromResponse(response) {
    try {
      logger.info('开始提取oauth_verifier', {
        status: response.status,
        hasHeaders: !!response.headers,
        hasData: !!response.data,
        hasRequest: !!response.request
      })

      // 1. 检查重定向URL中的verifier
      if (response.request && response.request.res && response.request.res.responseUrl) {
        const finalUrl = response.request.res.responseUrl
        logger.info('检查重定向URL', { finalUrl })
        const urlMatch = finalUrl.match(/oauth_verifier=([^&]+)/)
        if (urlMatch && urlMatch[1]) {
          logger.info('从重定向URL中找到oauth_verifier')
          return urlMatch[1]
        }
      }

      // 2. 检查响应体中的verifier
      const responseText = typeof response.data === 'string' ? response.data : JSON.stringify(response.data)
      logger.info('检查响应体', {
        length: responseText.length,
        containsVerifier: responseText.includes('oauth_verifier'),
        preview: responseText.substring(0, 200) + '...'
      })

      if (responseText && responseText.includes('oauth_verifier')) {
        const verifierMatch = responseText.match(/oauth_verifier=([^"&]+)/)
        if (verifierMatch && verifierMatch[1]) {
          logger.info('从响应体中找到oauth_verifier')
          return verifierMatch[1]
        }
      }

      // 3. 检查Location header
      if (response.headers && response.headers.location) {
        logger.info('检查Location header', { location: response.headers.location })
        const locationMatch = response.headers.location.match(/oauth_verifier=([^&]+)/)
        if (locationMatch && locationMatch[1]) {
          logger.info('从Location header中找到oauth_verifier')
          return locationMatch[1]
        }
      }

      // 4. 检查所有headers
      logger.info('所有响应headers', response.headers)

      // 5. 保存响应到文件以便调试
      const fs = require('fs')
      fs.writeFileSync('./debug_oauth_response.html', responseText)
      logger.info('响应已保存到 debug_oauth_response.html')

      logger.warn('未在任何地方找到oauth_verifier')
      return null

    } catch (error) {
      logger.error('提取oauth_verifier失败', error.message)
      return null
    }
  }

  /**
   * 获取OAuth verifier - 主要公共接口
   * @param {string} oauthToken OAuth token
   * @returns {Promise<Object>} 包含oauth_verifier的结果
   */
  async getOAuthVerifier(oauthToken) {
    const result = await this.authorizeWithTwitter(oauthToken)
    if (result.success) {
      return {
        success: true,
        oauthVerifier: result.oauthVerifier,
        oauthToken: oauthToken,
        authenticityToken: this.state.authenticityToken
      }
    } else {
      return result
    }
  }

  /**
   * 兼容性方法 - Twitter OAuth授权
   * @param {string} oauthToken OAuth token
   * @returns {Promise<boolean>} 是否成功
   */
  async twitterAuthorize(oauthToken) {
    const result = await this.authorizeWithTwitter(oauthToken)
    return result.success
  }

  /**
   * 重置状态
   */
  resetState() {
    this.state = {
      authenticityToken: null,
      oauthVerifier: null,
      csrfToken: null
    }
  }

  /**
   * 获取当前状态
   */
  getState() {
    return { ...this.state }
  }

  /**
   * 兼容性方法 - 获取Twitter token
   * @param {string} oauthToken OAuth token
   * @returns {Promise<boolean>} 是否成功
   */
  async getTwitterToken(oauthToken) {
    return await this.getTwitterTokens(oauthToken)
  }

  /**
   * 兼容性方法 - 获取authenticity token
   * @returns {string|null} Authenticity token
   */
  getAuthenticityToken() {
    return this.state.authenticityToken
  }

  /**
   * 兼容性方法 - 获取oauth verifier
   * @returns {string|null} OAuth verifier
   */
  getOauthVerifier() {
    return this.state.oauthVerifier
  }

  /**
   * 兼容性方法 - 重置tokens
   */
  resetTokens() {
    this.resetState()
  }
}

// 为了兼容性，同时导出两个类名
class Twitter extends TwitterOAuth {}

module.exports = TwitterOAuth
module.exports.Twitter = Twitter
